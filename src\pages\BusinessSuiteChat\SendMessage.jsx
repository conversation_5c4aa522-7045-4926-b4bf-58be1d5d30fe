import {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  selectSelectedChat,
  selectActiveFilter,
  selectSelectedPage,
  sendMessage,
  selectDisabledChat,
  setDisabledChat,
  setDisabledChatLimit,
  selectSelectedWhatsappChat,
  selectSelectedPhone,
  sendWhatsMessage,
  selectSelectedWhatsAccount,
} from "../../redux/features/metaBusinessChatSlice";
import { FaPaperclip, FaTimes } from "react-icons/fa";
import { FaMicrophone, FaStop, FaPaperPlane } from "react-icons/fa6";
import { BsEmojiSmile } from "react-icons/bs";
import { RiEmojiStickerLine } from "react-icons/ri"; // Add sticker icon
import { useSelector, useDispatch } from "react-redux";
import { Spinner } from "react-bootstrap";
import EmojiPicker from "emoji-picker-react";
import AudioVisualizer from "../../components/AudioVisualizer/AudioVisualizer";
import MicRecorder from "mic-recorder-to-mp3";
import "./SendMessage.css";

// Import sticker assets
import sticker1 from "../../assets/media/sticker_1.png";
import sticker2 from "../../assets/media/sticker_6.png";
import sticker3 from "../../assets/media/sticker_23.png";
import sticker4 from "../../assets/media/sticker_30.png";
import sticker5 from "../../assets/media/animated.gif";

const SendMessage = forwardRef(
  ({ addSendingMessage, removeSendingMessage }, ref) => {
    const {
      selectedChat,
      disabledChat,
      selectedPage,
      activeFilter,
      selectedWhatsAccount,
      selectedPhone,
      selectedWhatsappChat,
    } = useSelector((state) => state.metaBusinessSuite);

    const dispatch = useDispatch();

    const [messageText, setMessageText] = useState("");
    const [attachment, setAttachment] = useState(null);
    const [sendingMessage, setSendingMessage] = useState(false);
    const [sendingAttachment, setSendingAttachment] = useState(false);

    // Emoji picker state
    const [showEmojiPicker, setShowEmojiPicker] = useState(false);
    const emojiButtonRef = useRef(null);
    const emojiPickerRef = useRef(null);

    // Add sticker-related state
    const [showStickerPicker, setShowStickerPicker] = useState(false);
    const [selectedSticker, setSelectedSticker] = useState(null);
    const [stickerFile, setStickerFile] = useState(null);
    const stickerButtonRef = useRef(null);
    const stickerPickerRef = useRef(null);

    // Sample stickers using local assets
    const stickers = [
      { id: 1, url: sticker1, name: "Thumbs Up" },
      { id: 2, url: sticker2, name: "Laughing" },
      { id: 3, url: sticker3, name: "Heart" },
      { id: 4, url: sticker4, name: "OK" },
      { id: 5, url: sticker5, name: "Animated" },
    ];

    // Audio recording state variables
    const [isRecording, setIsRecording] = useState(false);
    const [audioBlob, setAudioBlob] = useState(null);
    const [mediaRecorder, setMediaRecorder] = useState(null);
    const [audioLevel, setAudioLevel] = useState(0);
    const audioChunks = useRef([]);
    const audioContext = useRef(null);
    const analyser = useRef(null);
    const dataArray = useRef(null);
    const audioSource = useRef(null);
    const audioStream = useRef(null);
    const mp3Recorder = useRef(null);

    // Initialize MP3 recorder on component mount
    useEffect(() => {
      mp3Recorder.current = new MicRecorder({ bitRate: 128 });

      return () => {
        // Clean up on unmount
        if (audioStream.current) {
          audioStream.current.getTracks().forEach((track) => track.stop());
        }
        if (audioContext.current && audioContext.current.state !== "closed") {
          audioContext.current.close();
        }
      };
    }, []);

    const fileInputRef = useRef(null);
    const { user } = useSelector((state) => state.auth);

    // Update hasContent to include stickers
    const hasContent = () =>
      messageText.trim() ||
      attachment ||
      audioBlob ||
      isRecording ||
      selectedSticker;

    // Handle sticker selection
    const handleStickerSelect = (sticker) => {
      // Simply set the selected sticker
      setSelectedSticker(sticker);

      // Close the sticker picker
      setShowStickerPicker(false);
    };

    // Handle removing selected sticker
    const handleRemoveSticker = () => {
      setSelectedSticker(null);
      setStickerFile(null);
    };

    // Close sticker picker when clicking outside
    useEffect(() => {
      const handleClickOutside = (event) => {
        if (
          stickerPickerRef.current &&
          !stickerPickerRef.current.contains(event.target) &&
          !stickerButtonRef.current?.contains(event.target)
        ) {
          setShowStickerPicker(false);
        }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }, []);

    // Start recording function
    const startRecording = async () => {
      try {
        if (!mp3Recorder.current) return;

        // Get audio stream for visualization
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
          },
        });

        audioStream.current = stream;

        // Set up audio context for visualization
        audioContext.current = new (window.AudioContext ||
          window.webkitAudioContext)();
        analyser.current = audioContext.current.createAnalyser();
        analyser.current.fftSize = 256;
        dataArray.current = new Uint8Array(analyser.current.frequencyBinCount);
        const source = audioContext.current.createMediaStreamSource(stream);
        source.connect(analyser.current);

        // Start the visualization loop
        const updateVisualization = () => {
          if (isRecording) {
            analyser.current.getByteFrequencyData(dataArray.current);
            const average =
              dataArray.current.reduce((a, b) => a + b) /
              dataArray.current.length;
            setAudioLevel(average);
            requestAnimationFrame(updateVisualization);
          }
        };

        // Start recording with mic-recorder-to-mp3
        await mp3Recorder.current.start();
        setIsRecording(true);
        updateVisualization();
      } catch (error) {
        console.error("Error accessing microphone:", error);
      }
    };

    // Stop recording function
    const stopRecording = () => {
      if (mp3Recorder.current && isRecording) {
        mp3Recorder.current
          .stop()
          .getMp3()
          .then(([buffer, blob]) => {
            // Create MP3 blob
            setAudioBlob(blob);
            setIsRecording(false);

            // Stop the audio stream
            if (audioStream.current) {
              audioStream.current.getTracks().forEach((track) => track.stop());
            }
          })
          .catch((error) => {
            console.error("Error stopping recording:", error);
            setIsRecording(false);
          });
      }
    };

    // Cancel recording function
    const cancelRecording = () => {
      if (mp3Recorder.current && isRecording) {
        mp3Recorder.current.stop();
        setIsRecording(false);
        setAudioLevel(0);

        // Stop the audio stream
        if (audioStream.current) {
          audioStream.current.getTracks().forEach((track) => track.stop());
        }
      }
      setAudioBlob(null);
    };

    // Add emoji click handler
    const onEmojiClick = (emojiObject) => {
      setMessageText((prev) => prev + emojiObject.emoji);
      setShowEmojiPicker(false);
    };

    // Add click outside handler to close emoji picker
    useEffect(() => {
      const handleClickOutside = (event) => {
        if (
          emojiPickerRef.current &&
          !emojiPickerRef.current.contains(event.target) &&
          !emojiButtonRef.current.contains(event.target)
        ) {
          setShowEmojiPicker(false);
        }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }, []);

    const handleSendMessage = async (e) => {
      let attachmentType;
      let directAudioBlob = null;
      e.preventDefault();

      // If recording is in progress, stop it first and capture the audio
      if (isRecording && mp3Recorder.current) {
        try {
          // Stop the recorder and get MP3 data
          const [buffer, blob] = await mp3Recorder.current.stop().getMp3();
          setIsRecording(false);

          // Use the MP3 blob directly
          directAudioBlob = blob;
          attachmentType = "audio";

          // Stop the audio stream
          if (audioStream.current) {
            audioStream.current.getTracks().forEach((track) => track.stop());
          }
        } catch (error) {
          console.error("Error stopping recording:", error);
          setIsRecording(false);
          return;
        }
      }

      // Double-check to prevent sending empty messages
      if (
        !messageText.trim() &&
        !attachment &&
        !audioBlob &&
        !directAudioBlob &&
        !selectedSticker
      )
        return;

      setSendingMessage(true);
      if (attachment || selectedSticker) {
        setSendingAttachment(true);
      }

      // Create a temporary message ID
      const tempMessageId = `temp_${Date.now()}`;

      // If we don't have a direct audio blob, determine attachment type normally
      if (!attachmentType) {
        attachmentType = selectedSticker
          ? "sticker"
          : attachment
          ? attachment.type.includes("image")
            ? "image"
            : attachment.type.includes("video")
            ? "video"
            : attachment.type.includes("audio")
            ? "audio"
            : "file"
          : audioBlob
          ? "audio"
          : null;
      }

      // Create temporary URL for preview
      const tempFileUrl = selectedSticker
        ? selectedSticker.url
        : attachment
        ? URL.createObjectURL(attachment)
        : audioBlob
        ? URL.createObjectURL(audioBlob)
        : directAudioBlob
        ? URL.createObjectURL(directAudioBlob)
        : null;

      // Store for sending - prioritize direct audio blob if it exists
      const attachmentToSend = directAudioBlob || attachment || audioBlob;

      // Clear attachments
      setAttachment(null);
      setAudioBlob(null);

      try {
        if (activeFilter === "whatsapp") {
          if (
            !selectedWhatsAccount ||
            !selectedWhatsappChat ||
            !selectedPhone
          ) {
            return;
          }

          const formData = new FormData();
          formData.append("to", selectedWhatsappChat?.sender_phone_number);
          formData.append("id", selectedPhone?.id);
          formData.append("access_token", user?.user["access-token"]);

          // Create temporary message for display
          if (selectedSticker) {
            // Handle sticker message
            const tempMessage = {
              id: tempMessageId,
              type: "sticker",
              message: selectedSticker.url,
              url: selectedSticker.url,
              sender: selectedPhone?.display_phone_number
                .trim()
                .replace(/^\+|\s+/g, ""),
              recipient: selectedWhatsappChat?.sender_phone_number
                .trim()
                .replace(/^\+|\s+/g, ""),
              created_time: new Date().toISOString(),
            };

            addSendingMessage(tempMessage);

            // We need to fetch the sticker to get its binary data
            fetch(selectedSticker.url)
              .then((response) => response.blob())
              .then((blob) => {
                // Get the file extension from the URL
                const urlParts = selectedSticker.url.split(".");
                const fileExtension =
                  urlParts[urlParts.length - 1].split("?")[0]; // Remove query params if any

                // Determine MIME type based on extension
                let mimeType;
                switch (fileExtension.toLowerCase()) {
                  case "jpg":
                  case "jpeg":
                    mimeType = "image/jpeg";
                    break;
                  case "png":
                    mimeType = "image/png";
                    break;
                  case "gif":
                    mimeType = "image/gif";
                    break;
                  case "webp":
                    mimeType = "image/webp";
                    break;
                  case "avif":
                    mimeType = "image/avif";
                    break;
                  default:
                    mimeType = blob.type || "image/png"; // Fallback to blob type or PNG
                }

                // Create a File object from the blob with the original extension
                const stickerFile = new File(
                  [blob],
                  `sticker-${
                    selectedSticker.id || Date.now()
                  }.${fileExtension}`,
                  { type: mimeType }
                );

                // Create a new FormData for the sticker
                const stickerFormData = new FormData();
                stickerFormData.append(
                  "to",
                  selectedWhatsappChat?.sender_phone_number
                );
                stickerFormData.append("id", selectedPhone?.id);
                stickerFormData.append(
                  "access_token",
                  user?.user["access-token"]
                );
                stickerFormData.append("type", "sticker");
                stickerFormData.append("media", stickerFile);

                // Send the sticker message
                return dispatch(
                  sendWhatsMessage({
                    data: stickerFormData,
                    selectedPhone,
                    selectedWhatsAccount,
                    selectedWhatsappChat,
                  })
                );
              })
              .then((success) => {
                if (!success) {
                  console.error("Failed to send WhatsApp sticker");
                }
                // Remove the temporary message
                removeSendingMessage(tempMessageId);
              })
              .catch((error) => {
                console.error("Error sending sticker:", error);
                removeSendingMessage(tempMessageId);
              });

            // Clear selected sticker
            setSelectedSticker(null);

            // Return early since we're handling this asynchronously
            setSendingMessage(false);
            setSendingAttachment(false);
            return;
          } else if (attachmentToSend) {
            // Use the already determined attachmentType
            let mediaType = attachmentType;

            // Add temporary message to show loading state
            const tempMessage = {
              id: tempMessageId,
              type: mediaType,
              message: tempFileUrl,
              // Add URL field for all media messages
              url:
                mediaType === "image" ||
                mediaType === "video" ||
                mediaType === "audio"
                  ? tempFileUrl
                  : null,
              sender: selectedPhone?.display_phone_number
                .trim()
                .replace(/^\+|\s+/g, ""),
              recipient: selectedWhatsappChat?.sender_phone_number
                .trim()
                .replace(/^\+|\s+/g, ""),
              created_time: new Date().toISOString(),
            };

            addSendingMessage(tempMessage);

            formData.append("media", attachmentToSend);
            formData.append("type", mediaType);
          } else if (messageText.trim() !== "") {
            // Add temporary text message
            const tempMessage = {
              id: tempMessageId,
              type: "text",
              message: messageText,
              sender: selectedPhone?.display_phone_number
                .trim()
                .replace(/^\+|\s+/g, ""),
              recipient: selectedWhatsappChat?.sender_phone_number
                .trim()
                .replace(/^\+|\s+/g, ""),
              created_time: new Date().toISOString(),
            };

            addSendingMessage(tempMessage);

            formData.append("text", messageText);
            formData.append("type", "text");
          }

          // Clear message text immediately
          setMessageText("");

          const result = await dispatch(
            sendWhatsMessage({
              data: formData,
              selectedPhone,
              selectedWhatsAccount,
              selectedWhatsappChat,
            })
          );

          // Remove the temporary message after a short delay to allow Firebase listener to pick up the real message
          setTimeout(() => {
            removeSendingMessage(tempMessageId);
          }, 1000);

          // Only clear input if message was sent successfully
          if (!result.payload) {
            console.error("Failed to send WhatsApp message");
          }
        } else {
          if (!selectedChat) return;

          const recipientId =
            selectedChat?.flage === "instagram"
              ? selectedChat?.participants?.data[1]?.id
              : selectedChat?.participants?.data[0]?.id;

          const senderId =
            selectedChat?.flage === "instagram"
              ? selectedChat?.participants?.data[0]?.id
              : selectedChat?.participants?.data[1]?.id;

          const formData = new FormData();
          formData.append("id", recipientId);
          formData.append("access_token", selectedPage?.page_token);

          // Create temporary message for display
          if (attachmentToSend) {
            // Add temporary message to show loading state
            const tempMessage = {
              id: tempMessageId,
              type: attachmentType,
              message: tempFileUrl,
              from: { id: senderId },
              created_time: new Date().toISOString(),
            };

            addSendingMessage(tempMessage);

            formData.append("file", attachmentToSend);
            formData.append("type", attachmentType);
          } else if (messageText.trim() !== "") {
            // Add temporary text message
            const tempMessage = {
              id: tempMessageId,
              type: "text",
              message: messageText,
              from: { id: senderId },
              created_time: new Date().toISOString(),
            };

            addSendingMessage(tempMessage);

            formData.append("message", messageText);
          }

          // Clear message text immediately
          setMessageText("");

          // Call sendMessage and get the result
          const result = await dispatch(
            sendMessage({
              data: formData,
              selectedChat,
            })
          );

          // Remove the temporary message
          removeSendingMessage(tempMessageId);

          // Check if the message was sent successfully (same as Context API)
          if (!result.payload) {
            console.error("Failed to send message");
          }
        }
      } catch (error) {
        // Remove the temporary message on error
        removeSendingMessage(tempMessageId);

        console.error("Error sending message:", error);
        const errorMessage =
          error?.response?.data?.message || error?.message || "";
        const errorStatus = error?.response?.status || error?.status || 0;
        const errorCode = error?.response?.data?.error?.code || 0;

        if (
          errorMessage.toLowerCase().includes("Limit") ||
          errorStatus === 429 ||
          errorCode === 4 ||
          (errorStatus === 400 && errorMessage.toLowerCase().includes("limit"))
        ) {
          dispatch(setDisabledChatLimit(true));
        }

        // Check for 24-hour window error in the catch block as well
        if (
          errorCode === 10 ||
          errorMessage.includes("(#10)") ||
          errorMessage.toLowerCase().includes("outside the allowed window")
        ) {
          dispatch(setDisabledChat(true));
          setMessageText("");
          setAttachment(null);
        }
      } finally {
        setSendingMessage(false);
        setSendingAttachment(false);
      }
    };

    const handleFileChange = (e) => {
      const selectedFile = e.target.files?.[0];
      if (selectedFile) {
        // Validate file size (Meta API has limits)
        const maxSize = 25 * 1024 * 1024; // 25MB limit
        if (selectedFile.size > maxSize) {
          console.error("File too large. Maximum size is 25MB");
          alert("File too large. Maximum size is 25MB");
          e.target.value = "";
          return;
        }

        // Validate file type
        const allowedTypes = [
          "image/jpeg",
          "image/jpg",
          "image/png",
          "image/gif",
          "image/webp",
          "video/mp4",
          "video/avi",
          "video/mov",
          "video/wmv",
          "audio/mp3",
          "audio/wav",
          "audio/ogg",
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "text/plain",
          "application/zip",
          "application/x-rar-compressed",
        ];

        if (!allowedTypes.includes(selectedFile.type)) {
          console.error("File type not supported:", selectedFile.type);
          alert(
            "File type not supported. Please select an image, video, audio, or document file."
          );
          e.target.value = "";
          return;
        }

        setAttachment(selectedFile);
      }
      // Reset the file input to allow the same file to be selected again
      e.target.value = "";
    };

    // Expose handleFileUpload method to parent component via ref
    useImperativeHandle(ref, () => ({
      handleFileUpload: (file) => {
        if (file) {
          setAttachment(file);
        }
      },
    }));

    const handleRemoveAttachment = () => {
      setAttachment(null);
    };

    return (
      <form className="send-message" onSubmit={handleSendMessage}>
        <div className="d-flex flex-column relative flex-grow-1 align-content-center">
          <div className={"input-container d-flex align-items-center gap-2"}>
            <input
              id="messageInput"
              name="messageInput"
              type="text"
              className="form-input__input flex-grow-1"
              placeholder={`Type ${activeFilter} message...`}
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
              disabled={sendingMessage}
            />

            {/* Enhanced audio recording button - only for WhatsApp */}
            {activeFilter === "whatsapp" &&
              (!isRecording ? (
                <button
                  type="button"
                  title="Record audio"
                  className="audio-record-btn"
                  onClick={startRecording}
                  disabled={sendingMessage || attachment}
                >
                  <FaMicrophone className="text-danger" size={20} />
                </button>
              ) : (
                <button
                  type="button"
                  title="Stop recording"
                  className="audio-stop-btn"
                  onClick={stopRecording}
                >
                  <FaStop className="text-danger" size={20} />
                </button>
              ))}

            <label
              htmlFor="fileInput"
              title="Attach file"
              className="attachment-label cursor-pointer"
            >
              <FaPaperclip
                className="attachment-icon text-gray-500 hover:text-gray-700"
                size={20}
              />
            </label>
            <input
              ref={fileInputRef}
              id="fileInput"
              type="file"
              accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar"
              onChange={handleFileChange}
              style={{ display: "none" }}
              disabled={sendingMessage || isRecording}
            />

            {/* Sticker button - only for WhatsApp */}
            {activeFilter === "whatsapp" && (
              <button
                type="button"
                title="Send sticker"
                className="sticker-btn"
                onClick={() => {
                  setShowStickerPicker(!showStickerPicker);
                  setShowEmojiPicker(false);
                }}
                disabled={disabledChat || isRecording}
                ref={stickerButtonRef}
              >
                <RiEmojiStickerLine size={25} />
              </button>
            )}

            <button
              type="button"
              className="emoji-btn"
              onClick={() => {
                setShowEmojiPicker(!showEmojiPicker);
                setShowStickerPicker(false);
              }}
              disabled={disabledChat || isRecording}
              ref={emojiButtonRef}
            >
              <BsEmojiSmile />
            </button>

            {showEmojiPicker && (
              <div className="emoji-picker-container" ref={emojiPickerRef}>
                <EmojiPicker
                  onEmojiClick={onEmojiClick}
                  searchDisabled={false}
                  skinTonesDisabled={false}
                  width={300}
                  height={400}
                  previewConfig={{
                    showPreview: true,
                  }}
                />
              </div>
            )}

            {/* Sticker picker */}
            {showStickerPicker && (
              <div className="sticker-picker-container" ref={stickerPickerRef}>
                <div className="sticker-grid">
                  {stickers.map((sticker) => (
                    <div
                      key={sticker.id}
                      className="sticker-item"
                      onClick={() => handleStickerSelect(sticker)}
                    >
                      <img
                        src={sticker.url}
                        alt={sticker.name}
                        title={sticker.name}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Send button as icon */}
            <button
              type="submit"
              title="Send message"
              className="send-icon-btn"
              disabled={sendingMessage || !hasContent()}
            >
              {sendingMessage ? (
                <Spinner animation="border" size="sm" variant="primary" />
              ) : (
                <FaPaperPlane className="text-primary" size={20} />
              )}
            </button>
          </div>

          {/* Audio visualizer during recording - only for WhatsApp */}
          {activeFilter === "whatsapp" && isRecording && (
            <AudioVisualizer
              isRecording={isRecording}
              audioLevel={audioLevel}
            />
          )}

          {/* Audio preview after recording - only for WhatsApp */}
          {activeFilter === "whatsapp" && audioBlob && !isRecording && (
            <div className="attachment-preview-container mt-2">
              <div className="attachment-preview relative">
                <div className="audio-preview">
                  <audio
                    src={URL.createObjectURL(audioBlob)}
                    controls
                    className="audio-player"
                  />
                  <button
                    type="button"
                    className="remove-btn"
                    onClick={cancelRecording}
                    disabled={sendingMessage}
                  >
                    <FaTimes
                      className="text-red-500 hover:text-red-700"
                      size={20}
                    />
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Existing attachment preview */}
          {attachment && (
            <div className="attachment-preview-container mt-2">
              <div className="attachment-preview relative">
                {attachment.type.startsWith("image/") ? (
                  <div className="position-relative">
                    <img
                      src={URL.createObjectURL(attachment)}
                      alt="Preview"
                      className="preview-image"
                      style={{ opacity: sendingAttachment ? 0.5 : 1 }}
                    />
                    {sendingAttachment && (
                      <div className="position-absolute top-50 start-50 translate-middle">
                        <Spinner animation="border" variant="primary" />
                      </div>
                    )}
                  </div>
                ) : attachment.type.startsWith("video/") ? (
                  <div className="position-relative">
                    <video
                      src={URL.createObjectURL(attachment)}
                      className="preview-image"
                      style={{ opacity: sendingAttachment ? 0.5 : 1 }}
                      controls
                    />
                    {sendingAttachment && (
                      <div className="position-absolute top-50 start-50 translate-middle">
                        <Spinner animation="border" variant="primary" />
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="file-info text-center">
                    <div className="file-name">{attachment.name}</div>
                    {sendingAttachment && (
                      <Spinner
                        animation="border"
                        size="sm"
                        variant="primary"
                        className="ms-2"
                      />
                    )}
                  </div>
                )}
                <button
                  type="button"
                  className="remove-btn"
                  onClick={handleRemoveAttachment}
                  disabled={sendingAttachment}
                >
                  <FaTimes
                    className="text-red-500 hover:text-red-700"
                    size={20}
                  />
                </button>
              </div>
            </div>
          )}

          {/* Sticker preview */}
          {selectedSticker && (
            <div className="attachment-preview-container mt-2">
              <div className="attachment-preview relative">
                <img
                  src={selectedSticker.url}
                  alt="Sticker"
                  className="preview-image sticker-preview"
                  style={{ maxWidth: "120px", maxHeight: "120px" }}
                />
                <button
                  type="button"
                  className="remove-attachment-btn"
                  onClick={handleRemoveSticker}
                  disabled={sendingMessage}
                >
                  <FaTimes size={20} />
                </button>
              </div>
            </div>
          )}
        </div>
      </form>
    );
  }
);

export default SendMessage;
