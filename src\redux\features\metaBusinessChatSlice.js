import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import {
  collection,
  deleteDoc,
  doc,
  getDocs,
  onSnapshot,
  orderBy,
  query,
  setDoc,
  where,
  collectionGroup,
} from "firebase/firestore";
import metaService from "../../services/integrations/meta";
import { toast } from "react-toastify";
import { showErrorToast } from "../../utils/toast-success-error";
import { db } from "../../utils/firebase.config";
import { BigIntUtil } from "../../utils/BigIntUtil";
import { createSelector } from 'reselect';

// Utility function to normalize phone numbers consistently
const normalizePhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return '';
  return phoneNumber.toString().trim().replace(/^\+|\s+/g, '');
};

// Helper function to check for 24-hour window errors
const check24HourWindowError = (error) => {
  if (!error) return false;

  // Check error object structure
  if (error.code === 10 || error.error_subcode === 2018278) return true;

  // Check error message content
  if (error.message && (
    error.message.includes("(#10)") ||
    error.message.toLowerCase().includes("outside the allowed window") ||
    error.message.toLowerCase().includes("24-hour")
  )) return true;

  return false;
};

// Helper function for consistent date parsing
const parseDate = (dateString) => {
  if (!dateString) return new Date(0);

  // Handle different date formats
  if (typeof dateString === 'string') {
    // If it contains a space but no 'T', replace space with 'T'
    if (dateString.includes(' ') && !dateString.includes('T')) {
      dateString = dateString.replace(' ', 'T');
    }
  }

  const date = new Date(dateString);
  return isNaN(date.getTime()) ? new Date(0) : date;
};

// Helper function to extract latest message (same as Context API)
const extractLatestMessage = (chatId, latestMessages) => {

  if (!latestMessages || !Array.isArray(latestMessages)) {
    return null;
  }

  // For WhatsApp, we need to check both sender and recipient fields
  // since we're now using sender ID as the document ID
  const senderMessage = latestMessages.find(
    (chat) => chat?.sender?.toString() === chatId?.toString(),
  );
  const recipientMessage = latestMessages.find(
    (chat) => chat?.recipient?.toString() === chatId?.toString(),
  );

  let latestMessage;
  if (senderMessage && recipientMessage) {
    latestMessage =
      new Date(senderMessage.created_time) >
        new Date(recipientMessage.created_time)
        ? senderMessage
        : recipientMessage;
  } else {
    latestMessage = senderMessage || recipientMessage;
  }

  return latestMessage ? {
    ...latestMessage,
    message: latestMessage?.message,
    message_id: latestMessage?.message_id,
    sender_name: latestMessage?.sender_name,
    created_time: latestMessage?.created_time || latestMessage?.updated_time,
  } : null;
};

// Helper function to format timestamp (same as Context API)
const formatTimestamp = (timestamp, currentDate) => {
  if (!timestamp) return "";

  const messageDate = new Date(timestamp);
  const messageDay = messageDate.toDateString();
  const currentDay = currentDate.toDateString();

  if (messageDay === currentDay) {
    return messageDate.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  } else {
    const yesterday = new Date(currentDate);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayDay = yesterday.toDateString();

    if (messageDay === yesterdayDay) {
      return "Yesterday";
    } else {
      return messageDate.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
    }
  }
};

const parsePhoneNumber = (phoneNumber) => {
  const numericPhoneNumber = phoneNumber.replace(/\D/g, "");
  return "+" + numericPhoneNumber;
};

// Async thunks for API calls
export const fetchLatestMessages = createAsyncThunk(
  "metaBusinessSuite/fetchLatestMessages",
  async (_, { rejectWithValue, dispatch, getState }) => {
    try {
      const state = getState().metaBusinessSuite;
      const selectedPage = state.selectedPage;

      if (!selectedPage?.id) {
        return { unsubscribe: null };
      }

      // Clean up any existing listener first to avoid multiple open channels
      const prevUnsub = state.latestMessagesUnsubscribe;
      if (prevUnsub) {
        try {
          prevUnsub();
        } catch (e) {
          console.warn("failed to unsubscribe previous listener", e);
        }
      }

      const messagesQuery = query(
        collection(db, "chats"),
        where("page_id", "==", selectedPage.id),
        orderBy("created_time", "asc")
      );

      // First, let's check what documents exist in the collection
      const testSnapshot = await getDocs(collection(db, "chats"));

      // Set up listener that continuously updates latestMessages (same as Context)
      const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
        const updatedMessages = snapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));
        dispatch(setLatestMessages(updatedMessages));
      }, (error) => {
        console.error('Firestore listener error:', error);
        // If the query fails (e.g., no page_id field), try without the where clause
        if (error.code === 'failed-precondition') {
          const fallbackQuery = query(
            collection(db, "chats"),
            orderBy("created_time", "asc")
          );

          const fallbackUnsubscribe = onSnapshot(fallbackQuery, (snapshot) => {
            const updatedMessages = snapshot.docs.map((doc) => ({
              id: doc.id,
              ...doc.data(),
            }));
            dispatch(setLatestMessages(updatedMessages));
          });

          dispatch(setLatestMessagesUnsubscribe(fallbackUnsubscribe));
        }
      });

      // Save listener reference in state via reducer after thunk resolves
      dispatch(setLatestMessagesUnsubscribe(unsubscribe));

      return { unsubscribe };
    } catch (error) {
      console.error("Error fetching latest messages:", error);
      return rejectWithValue(error.message);
    }
  }
);

export const fetchMessages = createAsyncThunk(
  "metaBusinessSuite/fetchMessages",
  async ({ thread }, { rejectWithValue, getState, dispatch }) => {
    if (!thread) {
      return { messages: [] };
    }
    const state = getState().metaBusinessSuite;
    try {
      const result = await metaService.getMessagesForChatApi({
        ...state.selectedPage,
        access_token: state?.selectedPage?.page_token,
        user_id: thread.id,
      });

      result.sort(
        (a, b) => parseDate(a.created_time) - parseDate(b.created_time),
      );

      const senderId =
        thread?.flage === "instagram"
          ? thread?.participants?.data[1]?.id
          : thread?.participants?.data[0]?.id;

      const messageFromFirestore = state?.latestMessages
        ? extractLatestMessage(senderId, state?.latestMessages)
        : null;

      let firestoreMessages = [];
      if (messageFromFirestore && messageFromFirestore.id) {
        const nestedCollectionRef = collection(
          db,
          "chats",
          messageFromFirestore.id,
          "messages",
        );
        const nestedQuery = query(
          nestedCollectionRef,
          orderBy("created_time", "asc"),
        );

        // Initial fetch
        const firestoreSnapshot = await getDocs(nestedQuery);
        firestoreMessages = firestoreSnapshot.docs.map((doc) => {
          const data = doc.data();
          return {
            id: doc.id,
            created_time: data?.created_time,
            from: {
              id: new BigIntUtil(data?.sender)?.value,
              email: new BigIntUtil(data?.sender)?.value,
            },
            message: data?.message,
            type: data?.type,
          };
        });

        // Realtime listener for additional messages in this chat
        const unsubscribeNested = onSnapshot(nestedQuery, (snapshot) => {
          const liveMessages = snapshot.docs.map((doc) => {
            const data = doc.data();
            return {
              id: doc.id,
              created_time: data?.created_time,
              from: {
                id: new BigIntUtil(data?.sender)?.value,
                email: new BigIntUtil(data?.sender)?.value,
              },
              message: data?.message,
              type: data?.type,
            };
          });

          // Only add messages to the active chat window
          dispatch(updateMessages(liveMessages));

        });

        // Save unsubscribe so it can be cleaned later
        dispatch(setMessagesSnapshotUnsubscribe(unsubscribeNested));
      }

      return {
        nestedMessages: result,
        messages: firestoreMessages,
        messageFromFirestore
      };
    } catch (error) {
      console.error("Error fetching messages:", error);
      return rejectWithValue(error.message);
    }
  }
);

export const fetchWhatsAppMessages = createAsyncThunk(
  "metaBusinessSuite/fetchWhatsAppMessages",
  async ({ thread, selectedPhone }, { rejectWithValue, dispatch, getState }) => {
    if (!thread || !selectedPhone) {
      return { apiMessages: [] };
    }

    try {
      // Fetch messages from API
      const result = await metaService.getMessagesForWhatsappChatApi({
        id: thread?.id,
        signal: null,
      });

      if (result?.data) {
        // Get the messages from the API response
        const apiMessages = result.data;

        // Update the selectedWhatsappChat with the latest timestamp
        if (apiMessages.length > 0) {
          const latestMessage = apiMessages.sort((a, b) =>
            new Date(b.created_at || b.timestamp) - new Date(a.created_at || b.timestamp)
          )[0];

          // Update the selected WhatsApp chat with the new timestamp
          dispatch(updateSelectedWhatsappChat((prevChat) => {
            if (!prevChat) return prevChat;
            const updatedChat = {
              ...prevChat,
              updated_at: latestMessage.created_at || latestMessage.timestamp || new Date().toISOString()
            };
            return updatedChat;
          }));
        }

        // Sort API messages by timestamp
        apiMessages.sort((a, b) => new Date(a.created_at || a.timestamp) - new Date(b.created_at || b.timestamp));

        // Transform API messages to match our format (same as Context API)
        const formattedApiMessages = apiMessages.map(msg => {
          const formattedMsg = {
            id: msg.id || `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            message_id: msg.id || msg.message_id,
            created_time: msg.created_at || msg.timestamp,
            // Preserve the message exactly as received, including all spaces
            message: msg.message,
            // Add URL field for image messages
            url: msg.type === "image" ? msg.url : msg.message,
            type: msg.type || "text",
            sender: msg.from === selectedPhone?.id ? normalizePhoneNumber(selectedPhone?.display_phone_number) : normalizePhoneNumber(thread?.sender_phone_number),
            recipient: msg.from === selectedPhone?.id ? normalizePhoneNumber(thread?.sender_phone_number) : normalizePhoneNumber(selectedPhone?.display_phone_number)
          };

          return formattedMsg;
        });

        // Set initial messages from API immediately
        dispatch(setWhatsappChatMessages(formattedApiMessages));

        // Also update the chat list with the latest message
        if (formattedApiMessages.length > 0) {
          const latestMessage = formattedApiMessages[formattedApiMessages.length - 1];

          // Update the selected WhatsApp chat with the latest message
          dispatch(updateSelectedWhatsappChat((prevChat) => {
            if (!prevChat) return prevChat;
            return {
              ...prevChat,
              last_message: {
                message: latestMessage.message,
                from: latestMessage.sender,
                type: latestMessage.type || "text",
                message_id: latestMessage.message_id,
                timestamp: Math.floor(new Date(latestMessage.created_time).getTime() / 1000).toString(),
                created_at: latestMessage.created_time,
                updated_at: latestMessage.created_time,
                url: latestMessage.url || null
              },
              updated_time: latestMessage.created_time,
            };
          }));
        }

        // Get sender phone ID
        const senderPhoneId = normalizePhoneNumber(thread?.sender_phone_number);

        console.log("FetchWhatsAppMessages - Customer phone processing:", {
          raw: thread?.sender_phone_number,
          processed: senderPhoneId,
          thread
        });

        // Set up Firestore listener for real-time updates (same as Context API)
        let unsubscribe = null;

        if (senderPhoneId) {
          const nestedCollectionRef = collection(
            db,
            "whatsApp",
            senderPhoneId,
            "messages"
          );
          const nestedQuery = query(
            nestedCollectionRef,
            orderBy("created_time", "asc")
          );

          unsubscribe = onSnapshot(nestedQuery, (snapshot) => {
            console.log("Firebase listener triggered for:", senderPhoneId, "Changes:", snapshot.docChanges().length);
            console.log("Firebase collection path:", `whatsApp/${senderPhoneId}/messages`);

            // Log document changes for debugging
            snapshot.docChanges().forEach((change) => {
              console.log(`Document ${change.type}:`, change.doc.id, change.doc.data());
            });

            const nestedMessages = snapshot.docs.map((doc) => {
              const data = doc.data();
              return {
                id: doc.id,
                message_id: data?.message_id || doc.id,
                created_time: data?.created_time,
                message: data?.message,
                type: data?.type || "text",
                sender: data?.sender,
                sender_name: data?.sender_name,
                recipient: data?.recipient,
                url: data?.url || null
              };
            });

            console.log("Firebase messages received:", nestedMessages.length, nestedMessages);

            // If we have new messages, update the selectedWhatsappChat timestamp
            if (nestedMessages.length > 0) {
              const latestMessage = nestedMessages.sort((a, b) =>
                new Date(b.created_time) - new Date(a.created_time)
              )[0];

              dispatch(updateSelectedWhatsappChat((prevChat) => {
                if (!prevChat) return prevChat;
                return {
                  ...prevChat,
                  updated_at: latestMessage.created_time
                };
              }));
            }

            // Update whatsappChatMessages state – merging & sorting handled inside reducer
            dispatch(updateWhatsappChatMessages(nestedMessages));
          }, (error) => {
            console.error("Firebase listener error for WhatsApp messages:", error);
          });
        }

        const returnValue = {
          apiMessages: formattedApiMessages,
          updatedChat: thread,
          unsubscribe
        };

        console.log("FetchWhatsAppMessages returning:", {
          apiMessagesCount: formattedApiMessages.length,
          hasUnsubscribe: !!unsubscribe,
          senderPhoneId
        });

        return returnValue;
      } else {
        return { apiMessages: [], updatedChat: thread, unsubscribe: null };
      }
    } catch (error) {
      console.error("Error fetching WhatsApp messages:", error);
      return rejectWithValue(error.message);
    }
  }
);

// Fetch WhatsApp latest messages with Firebase listener
export const fetchWhatsAppLatestMessages = createAsyncThunk(
  "metaBusinessSuite/fetchWhatsAppLatestMessages",
  async ({ selectedPhone }, { rejectWithValue, getState, dispatch }) => {
    try {
      if (!selectedPhone) {
        return null;
      }

      // Get the business phone number in a consistent format
      const businessPhoneNumber = selectedPhone?.display_phone_number.trim().replace(/^\+|\s+/g, '');

      // Query WhatsApp documents where the sender or recipient matches the selected phone
      const whatsAppQuery = query(
        collection(db, "whatsApp"),
        orderBy("created_time", "desc")
      );

      // Set up polling to check for new messages every 10 seconds
      const startPolling = () => {
        // Clean up any existing polling interval
        const state = getState().metaBusinessSuite;
        if (state.pollingInterval) {
          clearInterval(state.pollingInterval);
        }

        // Store the polling interval in Redux state (null for now since polling is disabled)
        dispatch(setPollingInterval(null));
      };

      return new Promise((resolve) => {
        const unsubscribe = onSnapshot(whatsAppQuery, (snapshot) => {
          const firebaseChats = snapshot.docs
            .map((doc) => {
              const data = doc.data();

              // Only include chats where this business phone is the sender or recipient
              if (data.sender !== businessPhoneNumber && data.recipient !== businessPhoneNumber) {
                return null;
              }

              // The document ID is the customer's phone number
              const customerPhoneNumber = doc.id;
              // For chat display, we always want to show the customer's name/number
              const customerName = data.sender_name || customerPhoneNumber;

              // Determine the correct sender_name based on who sent the message
              let correctSenderName;
              if (data.sender === businessPhoneNumber) {
                // Business sent the message, use business name
                correctSenderName = data.sender_name || "Business";
              } else {
                // Customer sent the message, use customer name or phone number
                // For incoming messages from customers, we need to preserve their name
                correctSenderName = data.sender_name || customerPhoneNumber;
              }

              const firebaseChat = {
                // Store the original API ID if available, otherwise use document ID or generate one
                originalApiId: data.originalApiId || doc.id || `temp_${Date.now()}`, // Ensure we always have an ID
                sender_phone_number: customerPhoneNumber,
                // Always use customer name for chat display
                sender_name: customerName,
                // Store the actual sender name separately for the last message
                last_message_sender_name: correctSenderName,
                ...data,
                created_time: data.created_time,
                updated_time: data.created_time,
                created_at: data.created_time,
                updated_at: data.created_time,
                last_message: {
                  message: data.message,
                  from: data.sender,
                  type: data.type || "text",
                  message_id: data.message_id,
                  timestamp: Math.floor(new Date(data.created_time).getTime() / 1000).toString(),
                  created_at: data.created_time,
                  updated_at: data.created_time,
                  url: data.url || null
                },
                // Add a flag to identify this as a Firebase document
                fromFirebase: true
              };
              return firebaseChat;
            })
            .filter(chat => chat !== null); // Remove null entries

          // Dispatch the Redux action to update the state
          dispatch(setWhatsappChats(firebaseChats));

          // Also update latest messages for the merged selector - FIXED: Merge instead of replace
          const latestMessages = firebaseChats.map(chat => ({
            id: chat.sender_phone_number,
            sender: chat.sender,
            sender_name: chat.sender_name,
            recipient: chat.recipient,
            message: chat.message,
            message_id: chat.message_id,
            created_time: chat.created_time,
            updated_time: chat.created_time,
            created_at: chat.created_time,
            type: chat.type || "text",
            url: chat.url || null
          }));

          // Merge with existing latest messages instead of replacing
          dispatch(updateLatestMessages((prevLatestMessages) => {
            if (!prevLatestMessages) return latestMessages;

            // Create a map of existing messages by ID for quick lookup
            const existingMessagesMap = new Map();
            prevLatestMessages.forEach(msg => {
              const key = msg.id || msg.sender || msg.recipient;
              if (key) {
                existingMessagesMap.set(key, msg);
              }
            });

            // Merge new messages with existing ones
            latestMessages.forEach(newMsg => {
              const key = newMsg.id || newMsg.sender || newMsg.recipient;
              if (key) {
                const existingMsg = existingMessagesMap.get(key);
                if (existingMsg) {
                  // Update existing message if new one is more recent
                  const existingDate = new Date(existingMsg.created_time || existingMsg.created_at);
                  const newDate = new Date(newMsg.created_time || newMsg.created_at);
                  if (newDate > existingDate) {
                    existingMessagesMap.set(key, newMsg);
                  }
                } else {
                  // Add new message
                  existingMessagesMap.set(key, newMsg);
                }
              }
            });

            // Convert back to array and sort
            const result = Array.from(existingMessagesMap.values()).sort((a, b) => {
              const dateA = parseDate(a.created_time || a.created_at);
              const dateB = parseDate(b.created_time || b.created_at);
              return dateB - dateA; // Sort in descending order
            });

            return result;
          }));

          // Start polling if not already started
          const state = getState().metaBusinessSuite;
          if (!state.pollingInterval) {
            startPolling();
          }

          resolve({
            firebaseChats, unsubscribe: () => {
              // Clean up both the Firestore listener and the polling interval
              unsubscribe();
              const state = getState().metaBusinessSuite;
              if (state.pollingInterval) {
                clearInterval(state.pollingInterval);
                dispatch(setPollingInterval(null));
              }
            }
          });
        });
      });
    } catch (error) {
      console.error("Error fetching WhatsApp latest messages:", error);
      return rejectWithValue(error.message);
    }
  }
);

// Listen to any new document inside whatsApp/*/messages for the selected business phone
export const listenToAllWhatsappMessages = createAsyncThunk(
  "metaBusinessSuite/listenToAllWhatsappMessages",
  async ({ selectedPhone }, { dispatch, getState }) => {
    if (!selectedPhone) return;

    const businessNumber = selectedPhone.display_phone_number.trim().replace(/^\+|\s+/g, "");

    // Clean previous listener if any
    const prevUnsub = getState().metaBusinessSuite.latestMessagesUnsubscribe;
    if (prevUnsub) {
      try { prevUnsub(); } catch (e) { /* noop */ }
    }

    // Listen for both incoming and outgoing WhatsApp messages related to this business number
    const msgsQueryIncoming = query(
      collectionGroup(db, "messages"),
      where("recipient", "==", businessNumber),
      orderBy("created_time", "desc")
    );

    const msgsQueryOutgoing = query(
      collectionGroup(db, "messages"),
      where("sender", "==", businessNumber),
      orderBy("created_time", "desc")
    );

    const handleSnap = (snap) => {
      snap.docChanges().forEach((change) => {
        if (change.type !== "added") return;
        const data = change.doc.data();
        const customerPhone = change.doc.ref.parent.parent.id; // parent doc id

        // 1) merge into latestMessages
        dispatch(updateLatestMessages((prev = []) => {
          const rest = (prev || []).filter(
            (m) => !(m.sender === customerPhone || m.recipient === customerPhone)
          );
          return [
            {
              id: customerPhone,
              sender: data.sender,
              recipient: data.recipient,
              message: data.message,
              message_id: data.message_id,
              created_time: data.created_time,
              type: data.type || "text",
              url: data.url || null,
            },
            ...rest,
          ];
        }));

        // 2) update whatsappChats preview list
        dispatch(updateWhatsappChatsWithFirebase((prevChats = []) => {
          const others = prevChats.filter((c) => c.sender_phone_number !== customerPhone);
          const existing = prevChats.find((c) => c.sender_phone_number === customerPhone);
          const base = existing || {
            sender_phone_number: customerPhone,
            sender_name: data.sender_name || customerPhone,
          };

          return [
            {
              ...base,
              last_message: {
                message: data.message,
                from: data.sender,
                type: data.type || "text",
                message_id: data.message_id,
                timestamp: Math.floor(new Date(data.created_time).getTime() / 1000).toString(),
                created_at: data.created_time,
                updated_at: data.created_time,
                url: data.url || null,
              },
              updated_time: data.created_time,
            },
            ...others,
          ];
        }));

        // 3) append to open chat thread if the chat is currently selected
        const { selectedWhatsappChat } = getState().metaBusinessSuite;
        if (selectedWhatsappChat?.sender_phone_number === customerPhone) {
          dispatch(updateWhatsappChatMessages([
            { id: change.doc.id, ...data },
          ]));
        }
      });
    };

    const unsubIn = onSnapshot(msgsQueryIncoming, handleSnap);
    const unsubOut = onSnapshot(msgsQueryOutgoing, handleSnap);

    // store combined unsubscribe for cleanup
    dispatch(setLatestMessagesUnsubscribe(() => {
      try { unsubIn(); } catch (e) { /* noop */ }
      try { unsubOut(); } catch (e) { /* noop */ }
    }));
  }
);

// Select chat async thunk with Firebase cleanup
export const selectChatAsync = createAsyncThunk(
  "metaBusinessSuite/selectChatAsync",
  async ({ thread }, { dispatch, getState }) => {
    console.log(thread);

    try {
      // Reset disabled chat state when switching to a new chat
      dispatch(setDisabledChat(false));

      // Clean up previous listener
      const state = getState().metaBusinessSuite;
      if (state.messagesSnapshotUnsubscribe) {
        state.messagesSnapshotUnsubscribe();
      }

      // Clean up polling interval
      if (state.pollingInterval) {
        clearInterval(state.pollingInterval);
        dispatch(setPollingInterval(null));
      }

      // Delete the nested "messages" collection for the selected chat
      if (thread && thread.id) {
        const chatId1 = thread?.participants?.data[1]?.id.toString();
        const chatId2 = thread?.participants?.data[0]?.id.toString();
        const nestedCollectionRef1 = collection(
          db,
          "chats",
          chatId1,
          "messages",
        );
        const nestedCollectionRef2 = collection(
          db,
          "chats",
          chatId2,
          "messages",
        );

        const nestedDocs1 = await getDocs(nestedCollectionRef1);
        const nestedDocs2 = await getDocs(nestedCollectionRef2);

        const deletePromises1 = nestedDocs1.docs.map((doc) =>
          deleteDoc(doc.ref),
        );
        const deletePromises2 = nestedDocs2.docs.map((doc) =>
          deleteDoc(doc.ref),
        );

        await Promise.all([...deletePromises1, ...deletePromises2]);
      }

      // Fetch messages for the selected chat
      await dispatch(fetchMessages({ thread }));

      return thread;
    } catch (error) {
      console.error("Error selecting chat:", error);
      throw error;
    }
  }
);

// Select WhatsApp chat async thunk with Firebase cleanup
export const selectWhatsappChatAsync = createAsyncThunk(
  "metaBusinessSuite/selectWhatsappChatAsync",
  async ({ thread, selectedPhone }, { dispatch, getState }) => {
    try {
      // Reset disabled chat state when switching to a new chat
      dispatch(setDisabledChat(false));

      // Clean up previous listener
      const state = getState().metaBusinessSuite;
      if (state.messagesSnapshotUnsubscribe) {
        state.messagesSnapshotUnsubscribe();
      }

      // Clean up polling interval
      if (state.pollingInterval) {
        clearInterval(state.pollingInterval);
        dispatch(setPollingInterval(null));
      }

      // Delete the nested "messages" collection for the selected WhatsApp chat
      if (thread && thread.sender_phone_number) {
        const senderPhoneId = normalizePhoneNumber(thread.sender_phone_number);

        if (senderPhoneId) {
          const nestedCollectionRef = collection(
            db,
            "whatsApp",
            senderPhoneId,
            "messages"
          );

          const nestedDocs = await getDocs(nestedCollectionRef);
          const deletePromises = nestedDocs.docs.map((doc) => deleteDoc(doc.ref));

          await Promise.all(deletePromises);
        }
      }

      // Ensure the thread has the correct ID for fetching messages
      const threadWithId = {
        ...thread,
        id: thread.originalApiId || thread.id // Use originalApiId if available, otherwise use id
      };

      // Fetch messages for the selected WhatsApp chat using the numeric ID
      const fetchResult = await dispatch(fetchWhatsAppMessages({ thread: threadWithId, selectedPhone }));

      return thread;
    } catch (error) {
      console.error("Error selecting WhatsApp chat:", error);
      throw error;
    }
  }
);

export const sendMessage = createAsyncThunk(
  "metaBusinessSuite/sendMessage",
  async ({ data, selectedChat }, { rejectWithValue, dispatch, getState }) => {
    try {
      const response = await metaService.sendMessageApi(data);
      // Check for 24-hour window error in the response
      if (response && typeof response === 'object') {
        // Check if response has an error property
        if (response.error) {
          // Check for 24-hour window error (code 10)
          if (response.error.code === 10 ||
            response.error.error_subcode === 2018278 ||
            (response.error.message && response.error.message.includes("(#10)")) ||
            (response.error.message && response.error.message.toLowerCase().includes("outside the allowed window"))) {
            dispatch(setDisabledChat(true));
            return false; // Return false to indicate error
          }

          // Handle other API errors
          return false; // Return false to indicate error
        }
      }

      const currentDate = new Date();
      const formattedDate = currentDate.toISOString();

      // Ensure we have a valid selectedChat before proceeding
      if (!selectedChat || !selectedChat.participants || !selectedChat.participants.data) {
        console.error("Invalid selectedChat object:", selectedChat);
        return false;
      }

      // Safely extract recipient and sender IDs with fallbacks
      let recipient, sender;

      try {
        recipient = selectedChat?.flage === "instagram"
          ? (selectedChat.participants.data[1]?.id || "").toString()
          : (selectedChat.participants.data[0]?.id || "").toString();

        sender = selectedChat?.flage === "instagram"
          ? (selectedChat.participants.data[0]?.id || "").toString()
          : (selectedChat.participants.data[1]?.id || "").toString();
      } catch (error) {
        console.error("Error extracting recipient/sender IDs:", error);
        return false;
      }

      // Validate recipient ID
      if (!recipient) {
        console.error("Invalid recipient ID");
        return false;
      }

      // Determine message type based on data
      const messageType = data.get("type") ||
        (response?.file_url ?
          (response.file_url.match(/\.(jpeg|jpg|gif|png)$/) ? "image" :
            response.file_url.match(/\.(mp4|mov|wmv|avi)$/) ? "video" : "file")
          : "text");

      const newMessage = {
        id: response?.message_id || `msg_${Date.now()}`,
        type: messageType,
        sender: sender,
        recipient: recipient,
        message: response?.file_url ? response?.file_url : data.get("message"),
        created_time: formattedDate,
      };

      // --- WhatsApp Firestore updates & state sync ---

      try {
        // First, update the main chat document with latest message
        const chatDocRef = doc(db, "chats", recipient);

        const docData = {
          sender: sender,
          recipient: recipient,
          message: newMessage.message,
          created_time: formattedDate,
          updated_time: formattedDate,
          type: messageType
        };

        await setDoc(chatDocRef, docData);

        // Then, add the message to the nested messages collection
        const messageRef = doc(db, "chats", recipient, "messages", newMessage.id);
        await setDoc(messageRef, newMessage);

        // Update latest messages state
        dispatch(updateLatestMessages((prevLatestMessages) => {
          if (!prevLatestMessages) return prevLatestMessages;

          const updatedLatestMessages = prevLatestMessages.map(msg => {
            if (msg.recipient === recipient || msg.sender === recipient) {
              return {
                ...msg,
                message: newMessage.message,
                type: messageType,
                created_time: formattedDate,
                updated_time: formattedDate
              };
            }
            return msg;
          });

          return updatedLatestMessages.sort((a, b) =>
            parseDate(b.created_time) - parseDate(a.created_time)
          );
        }));

        // Update the appropriate chat list based on activeFilter
        const updateChatList = (prevChats) => {
          if (!prevChats) return prevChats;

          const updatedChats = prevChats.map(chat => {
            if (chat.id === selectedChat.id) {
              return {
                ...chat,
                updated_time: formattedDate,
                message: newMessage.message,
                type: messageType
              };
            }
            return chat;
          });

          return updatedChats.sort((a, b) =>
            parseDate(b.updated_time) - parseDate(a.updated_time)
          );
        };

        if (selectedChat?.flage === "instagram") {
          dispatch(updateInstagramChats(updateChatList));
        } else {
          dispatch(updateMessengerChats(updateChatList));
        }

        return true; // Return true to indicate success

      } catch (error) {
        console.error("Error updating Firebase:", error);
        return false;
      }
    } catch (error) {
      console.error("Error in sendMessage:", error);

      // Check for 24-hour window error in the caught error
      if (error && error.response && error.response.data && error.response.data.error) {
        const apiError = error.response.data.error;
        if (apiError.code === 10 ||
          apiError.error_subcode === 2018278 ||
          (apiError.message && apiError.message.includes("(#10)")) ||
          (apiError.message && apiError.message.toLowerCase().includes("outside the allowed window"))) {
          dispatch(setDisabledChat(true));
          return false;
        }
      }

      return false;
    }
  }
);

export const sendWhatsMessage = createAsyncThunk(
  "metaBusinessSuite/sendWhatsMessage",
  async ({ data, selectedPhone, selectedWhatsAccount, selectedChat, selectedWhatsappChat }, { rejectWithValue, dispatch, getState }) => {
    try {
      const response = await metaService.sendWhatsMessageApi(data);

      const fileUrl = response?.file_url || null;

      if (response?.error) {
        console.error("Error in WhatsApp API response:", response.error);
        return false;
      }

      // Create a new message object
      const currentDate = new Date();
      const formattedDate = currentDate.toISOString();

      // Get the sender phone ID from the data (passed from SendMessage.jsx)
      const senderPhoneId = data.get("id");
      // Business phone number (from the selected phone in context)
      const senderPhone = normalizePhoneNumber(selectedPhone?.display_phone_number) || '';

      // Determine customer phone number from either selectedWhatsappChat or selectedChat
      const customerPhoneNumberRaw = selectedWhatsappChat?.sender_phone_number || selectedChat?.sender_phone_number || '';
      const recipientPhone = normalizePhoneNumber(customerPhoneNumberRaw);

      if (!recipientPhone) {
        throw new Error('Invalid recipient phone number');
      }

      console.log("SendWhatsMessage - Customer phone processing:", {
        raw: customerPhoneNumberRaw,
        processed: recipientPhone,
        selectedWhatsappChat: selectedWhatsappChat?.sender_phone_number,
        selectedChat: selectedChat?.sender_phone_number
      });

      console.log("SendWhatsMessage - Will save message to Firebase path:", `whatsApp/${recipientPhone}/messages`);

      const senderName = selectedWhatsAccount?.name || "Business";
      const messageText = data.get("text") || "";
      const messageType = data.get("type") || "text";

      // For media messages (image, video, audio, sticker), store the URL in both message and url fields
      const isMediaMessage = messageType === "image" ||
        messageType === "video" ||
        messageType === "audio" ||
        messageType === "sticker";

      // Define senderId and recipientId for use in the function
      const senderId = senderPhone;
      const recipientId = recipientPhone;

      // Create the message object with the URL field for media messages
      const newMessage = {
        id: response?.messages?.[0]?.id || response?.message_id || `whats_${Date.now()}`,
        message_id: response?.messages?.[0]?.id || response?.message_id || `whats_${Date.now()}`,
        type: messageType,
        sender: senderPhone,
        sender_name: senderName,
        recipient: recipientPhone,
        message: isMediaMessage ? fileUrl : messageText,
        // Add URL field for media messages
        url: isMediaMessage ? fileUrl : null,
        created_time: formattedDate,
        // Add MIME type for audio messages
        mime_type: messageType === "audio" ? "audio/mp3" : null
      };

      try {
        // Update the selectedWhatsappChat with the new timestamp
        dispatch(updateSelectedWhatsappChat((prevChat) => {
          if (!prevChat) return prevChat;
          const updatedChat = {
            ...prevChat,
            updated_at: formattedDate
          };

          return updatedChat;
        }));

        // First, update the main whatsApp document with latest message
        // Using sender phone ID as the document ID as per requirement
        // IMPORTANT: Use the same phone number format as the listener expects
        // Use recipientPhone which is already consistently processed
        const normalizedRecipientPhone = recipientPhone;

        console.log("Saving message for recipient phone:", normalizedRecipientPhone);
        console.log("Saving message to Firebase path:", `whatsApp/${normalizedRecipientPhone}`);
        console.log("Selected WhatsApp chat sender_phone_number:", selectedWhatsappChat?.sender_phone_number);
        console.log("Phone number formatting comparison:", {
          recipientPhone,
          normalizedRecipientPhone,
          rawSenderPhone: selectedWhatsappChat?.sender_phone_number,
          processedSenderPhone: normalizePhoneNumber(selectedWhatsappChat?.sender_phone_number)
        });
        const chatDocRef = doc(db, "whatsApp", normalizedRecipientPhone);

        const docData = {
          sender: selectedPhone?.display_phone_number.trim().replace(/^\+|\s+/g, ''),
          // Store the customer name for chat display (not the business name)
          sender_name: selectedWhatsappChat?.sender_name || selectedWhatsappChat?.sender_phone_number,
          recipient: normalizePhoneNumber(selectedWhatsappChat?.sender_phone_number),
          message: isMediaMessage ? fileUrl : messageText,
          message_id: newMessage.message_id,
          created_time: formattedDate,
          type: messageType,
          // Add URL field for media messages
          url: isMediaMessage ? fileUrl : null,
          // Store the original API ID so we can reference it later
          originalApiId: selectedWhatsappChat?.id
        };

        await setDoc(chatDocRef, docData);
        console.log("Main WhatsApp document updated successfully for:", recipientPhone);

        // Then, add the message to the nested messages collection
        const messageRef = doc(db, "whatsApp", recipientPhone, "messages", newMessage.id);
        console.log("Saving message to Firebase messages path:", `whatsApp/${recipientPhone}/messages/${newMessage.id}`);
        await setDoc(messageRef, newMessage);
        console.log("Message added to Firebase successfully:", newMessage.id, "Path:", `whatsApp/${recipientPhone}/messages/${newMessage.id}`);

        // Immediately merge the sent message into the open chat thread
        console.log("About to add message to chat immediately:", newMessage);
        dispatch(updateWhatsappChatMessages([newMessage]));

        console.log("Sent message added to chat immediately:", newMessage);

        // Verify the message was added to state
        const currentState = getState().metaBusinessSuite;
        console.log("Current whatsappChatMessages count after adding:", currentState.whatsappChatMessages.length);
        console.log("Last message in chat:", currentState.whatsappChatMessages[currentState.whatsappChatMessages.length - 1]);

        // Additional debug logging to verify Firebase listener path matches storage path
        const { selectedWhatsappChat: currentSelectedChat } = getState().metaBusinessSuite;
        if (currentSelectedChat) {
          const listenerPhoneId = normalizePhoneNumber(currentSelectedChat.sender_phone_number);
          console.log("Firebase listener should be active for path:", `whatsApp/${listenerPhoneId}/messages`);
          console.log("Message stored at path:", `whatsApp/${recipientPhone}/messages`);
          console.log("Paths match:", listenerPhoneId === recipientPhone);

          if (listenerPhoneId !== recipientPhone) {
            console.error("PHONE NUMBER MISMATCH - Firebase listener won't pick up the message!");
            console.error("Listener phone:", listenerPhoneId);
            console.error("Storage phone:", recipientPhone);
            console.error("Selected chat:", currentSelectedChat);
          }
        }

        // Force a small delay to ensure Firebase write is complete before listener picks it up
        setTimeout(() => {
          console.log("Firebase write should be complete, listener should have triggered");
        }, 100);

        console.log("Message saved to Firebase:", {
          collection: `whatsApp/${recipientPhone}/messages`,
          messageId: newMessage.id,
          message: newMessage
        });

        // Update latest messages state - FIXED: Use proper phone number matching
        dispatch(updateLatestMessages((prevLatestMessages) => {
          if (!prevLatestMessages) return prevLatestMessages;

          const updatedLatestMessages = prevLatestMessages.map(msg => {
            // Match by phone numbers - check if this message is for the current chat
            const msgSender = msg.sender?.toString() || msg.from?.toString();
            const msgRecipient = msg.recipient?.toString();
            const currentSender = senderId?.toString();
            const currentRecipient = recipientId?.toString();

            if ((msgSender === currentSender && msgRecipient === currentRecipient) ||
              (msgSender === currentRecipient && msgRecipient === currentSender)) {
              return {
                ...msg,
                message: newMessage.message,
                message_id: newMessage.message_id,
                sender_name: senderName,
                created_time: formattedDate,
                updated_time: formattedDate,
                created_at: formattedDate,
                url: isMediaMessage ? fileUrl : null,
                type: messageType
              };
            }
            return msg;
          });

          // If no existing message was updated, add a new one
          const messageExists = updatedLatestMessages.some(msg => {
            const msgSender = msg.sender?.toString() || msg.from?.toString();
            const msgRecipient = msg.recipient?.toString();
            const currentSender = senderId?.toString();
            const currentRecipient = recipientId?.toString();

            return (msgSender === currentSender && msgRecipient === currentRecipient) ||
              (msgSender === currentRecipient && msgRecipient === currentSender);
          });

          if (!messageExists) {
            updatedLatestMessages.push({
              id: senderId,
              sender: senderId,
              // Use business name when business sends message
              sender_name: senderName,
              recipient: recipientId,
              message: newMessage.message,
              message_id: newMessage.message_id,
              created_time: formattedDate,
              updated_time: formattedDate,
              created_at: formattedDate,
              type: messageType,
              url: isMediaMessage ? fileUrl : null
            });
          }

          const sortedMessages = updatedLatestMessages.sort((a, b) =>
            parseDate(b.created_time) - parseDate(a.created_time)
          );

          return sortedMessages;
        }));

        // Update WhatsApp chats list - FIXED: Ensure proper chat matching and sorting
        dispatch(updateWhatsappChatsWithFirebase((prevChats) => {
          if (!prevChats) return prevChats;

          const updatedChats = prevChats.map(chat => {
            // Match by sender_phone_number to ensure we update the correct chat
            if (chat.sender_phone_number === selectedWhatsappChat.sender_phone_number) {
              const updatedChat = {
                ...chat,
                // Preserve the original numeric id from the API
                id: chat.id, // This should be the numeric ID from the API
                updated_at: formattedDate,
                last_message: {
                  ...chat.last_message,
                  message: isMediaMessage ? fileUrl : messageText,
                  from: senderPhone,
                  type: messageType,
                  // Add URL field for media messages
                  url: isMediaMessage ? fileUrl : null,
                  message_id: newMessage.message_id,
                  timestamp: Math.floor(currentDate.getTime() / 1000).toString(),
                  created_at: formattedDate,
                  updated_at: formattedDate
                }
              };

              return updatedChat;
            }
            return chat;
          });

          // Sort by latest message time
          const sortedChats = updatedChats.sort((a, b) => {
            const timeA = a.last_message?.created_at
              ? parseDate(a.last_message.created_at)
              : a.updated_at
                ? parseDate(a.updated_at)
                : parseDate(a.created_at);

            const timeB = b.last_message?.created_at
              ? parseDate(b.last_message.created_at)
              : b.updated_at
                ? parseDate(b.updated_at)
                : parseDate(b.created_at);

            return timeB - timeA; // Sort in descending order (newest first)
          });

          return sortedChats;
        }));

        return true; // Return true to indicate success

      } catch (error) {
        console.error("Error sending WhatsApp message:", error);
        return false;
      }
    } catch (error) {
      console.error("Error in sendWhatsMessage:", error);
      return rejectWithValue(error?.message || "Failed to send WhatsApp message");
    }
  }
);

// Fetch WhatsApp chats from API and sync with Firebase
export const fetchWhatsAppChatsFromAPI = createAsyncThunk(
  "metaBusinessSuite/fetchWhatsAppChatsFromAPI",
  async ({ selectedPhone }, { rejectWithValue, dispatch, getState }) => {
    try {
      if (!selectedPhone) {
        return [];
      }

      // Fetch chats from API
      const response = await metaService.getWhatsAppChatsForPageApi({
        id: selectedPhone.id,
      });

      if (!response?.data) {
        return [];
      }

      const apiChats = response.data;

      // Sync each API chat with Firebase
      for (const apiChat of apiChats) {
        try {
          const chatDocRef = doc(db, "whatsApp", apiChat.sender_phone_number);

          const docData = {
            sender: apiChat.last_message?.from === selectedPhone?.id ?
              selectedPhone?.display_phone_number.trim().replace(/^\+|\s+/g, '') :
              apiChat.sender_phone_number,
            sender_name: apiChat.sender_name,
            recipient: apiChat.last_message?.from === selectedPhone?.id ?
              apiChat.sender_phone_number :
              selectedPhone?.display_phone_number.trim().replace(/^\+|\s+/g, ''),
            message: apiChat.last_message?.message || "",
            message_id: apiChat.last_message?.message_id || apiChat.last_message?.id,
            created_time: apiChat.last_message?.created_at || apiChat.created_at,
            type: apiChat.last_message?.type || "text",
            url: apiChat.last_message?.url || null,
            originalApiId: apiChat.id // Store the API ID
          };

          await setDoc(chatDocRef, docData);
        } catch (error) {
          console.error('Error syncing API chat to Firebase:', apiChat.sender_phone_number, error);
        }
      }

      return apiChats;
    } catch (error) {
      console.error("Error fetching WhatsApp chats from API:", error);
      return rejectWithValue(error.message);
    }
  }
);

// Initial state
const initialState = {
  // Chat state
  chats: [],
  selectedChat: null,
  messages: [],
  nestedMessages: [],
  latestMessages: null,

  // Page and filter state
  selectedPage: null,
  activeFilter: "messenger",
  messengerChats: [],
  instagramChats: [],

  // WhatsApp state
  whatsappChats: [],
  whatsappChatMessages: [],
  whatsAppAccounts: [],
  selectedWhatsAccount: null,
  selectedWhatsappChat: null,
  whatsAppAccountDetails: [],
  selectedPhone: null,
  businessPhoneNumbers: [],

  // Filter and pagination state
  startDate: "",
  endDate: "",
  filteredData: [],
  conversations: [],
  paginationMeta: [null, null],
  hasMore: true,

  // UI state
  disabledChat: false,
  disabledChatLimit: false,
  pageImg: null,
  loadingPagination: false,
  loadingChats: false,

  // Loading states for async operations
  loading: {
    fetchingLatestMessages: false,
    fetchingMessages: false,
    fetchingWhatsAppMessages: false,
    sendingMessage: false,
    sendingWhatsMessage: false,
    fetchingMoreChats: false,
  },

  // Error states
  error: {
    fetchLatestMessages: null,
    fetchMessages: null,
    fetchWhatsAppMessages: null,
    sendMessage: null,
    sendWhatsMessage: null,
    fetchMoreChats: null,
    fetchChats: null,
  },

  // Firestore listeners
  messagesSnapshotUnsubscribe: null,
  latestMessagesUnsubscribe: null,
  pollingInterval: null,
};

// Create slice
const metaBusinessSuiteSlice = createSlice({
  name: "metaBusinessSuite",
  initialState,
  reducers: {
    // Basic setters
    setChats: (state, action) => {
      state.chats = action.payload;
    },
    setSelectedChat: (state, action) => {
      state.selectedChat = action.payload;
    },
    setMessages: (state, action) => {
      state.messages = action.payload;
    },
    setNestedMessages: (state, action) => {
      state.nestedMessages = action.payload;
    },
    setSelectedPage: (state, action) => {
      state.selectedPage = action.payload;
    },
    setLatestMessages: (state, action) => {
      state.latestMessages = action.payload;
    },
    setActiveFilter: (state, action) => {
      state.activeFilter = action.payload;
    },
    setMessengerChats: (state, action) => {
      state.messengerChats = action.payload;
    },
    setInstagramChats: (state, action) => {
      state.instagramChats = action.payload;
    },
    setWhatsappChats: (state, action) => {
      state.whatsappChats = action.payload;
    },
    setWhatsappChatMessages: (state, action) => {
      state.whatsappChatMessages = action.payload;
    },
    setWhatsAppAccounts: (state, action) => {
      state.whatsAppAccounts = action.payload;
    },
    setSelectedWhatsAccount: (state, action) => {
      state.selectedWhatsAccount = action.payload;
    },
    setSelectedWhatsappChat: (state, action) => {
      state.selectedWhatsappChat = action.payload;
    },
    setWhatsAppAccountDetails: (state, action) => {
      state.whatsAppAccountDetails = action.payload;
    },
    setSelectedPhone: (state, action) => {
      state.selectedPhone = action.payload;
    },
    setStartDate: (state, action) => {
      state.startDate = action.payload;
    },
    setEndDate: (state, action) => {
      state.endDate = action.payload;
    },
    setFilteredData: (state, action) => {
      state.filteredData = action.payload;
    },
    setConversations: (state, action) => {
      state.conversations = action.payload;
    },
    setBusinessPhoneNumbers: (state, action) => {
      state.businessPhoneNumbers = action.payload;
    },
    setDisabledChat: (state, action) => {
      state.disabledChat = action.payload;
    },
    setDisabledChatLimit: (state, action) => {
      state.disabledChatLimit = action.payload;
    },
    setPageImg: (state, action) => {
      state.pageImg = action.payload;
    },
    setPaginationMeta: (state, action) => {
      state.paginationMeta = action.payload;
    },
    setHasMore: (state, action) => {
      state.hasMore = action.payload;
    },
    setLoadingPagination: (state, action) => {
      state.loadingPagination = action.payload;
    },
    setLoadingChats: (state, action) => {
      state.loadingChats = action.payload;
    },
    setMessagesSnapshotUnsubscribe: (state, action) => {
      state.messagesSnapshotUnsubscribe = action.payload;
    },
    setLatestMessagesUnsubscribe: (state, action) => {
      state.latestMessagesUnsubscribe = action.payload;
    },
    setPollingInterval: (state, action) => {
      state.pollingInterval = action.payload;
    },
    selectChat: (state, action) => {
      const thread = action.payload;
      state.selectedChat = thread;
      state.messages = [];
      state.nestedMessages = [];
      state.disabledChat = false;

      // Clean up previous listener
      if (state.messagesSnapshotUnsubscribe) {
        state.messagesSnapshotUnsubscribe();
        state.nestedMessages = [];
        state.messagesSnapshotUnsubscribe = null;
      }
    },
    selectWhatsappChat: (state, action) => {
      const thread = action.payload;
      state.selectedChat = thread;
      state.whatsappChatMessages = [];
      state.nestedMessages = [];
      state.disabledChat = false;
      state.selectedWhatsappChat = thread;

      // Clean up previous listener
      if (state.messagesSnapshotUnsubscribe) {
        state.messagesSnapshotUnsubscribe();
        state.nestedMessages = [];
        state.messagesSnapshotUnsubscribe = null;
      }
    },
    updateWhatsappChatsWithFirebase: (state, action) => {
      state.whatsappChats = action.payload(state.whatsappChats);
    },
    handleSelectWhatsAppAccount: (state, action) => {
      const { whatsAppAccount, phone } = action.payload;
      state.selectedWhatsAccount = whatsAppAccount;
      state.selectedPhone = phone;
      state.businessPhoneNumbers = whatsAppAccount?.phone_numbers?.data?.map((account) => ({
        ...account,
        phone: parsePhoneNumber(account?.display_phone_number),
      })) || [];
    },
    resetFilteredChatsPages: (state) => {
      state.nestedMessages = [];
      state.messages = [];
    },
    updateMessages: (state, action) => {
      const newMessages = action.payload;
      const uniqueMessages = [...state.messages];

      newMessages.forEach(message => {
        if (!uniqueMessages.some((m) => m.id === message.id)) {
          uniqueMessages.push(message);
        }
      });

      uniqueMessages.sort(
        (a, b) => parseDate(a.created_time) - parseDate(b.created_time),
      );
      state.messages = uniqueMessages;

      // Also update latestMessages if we have a selected chat
      if (state.selectedChat && newMessages.length > 0) {
        const latestMessage = newMessages[newMessages.length - 1]; // Get the most recent message

        // Find the participant ID for the selected chat
        const participantIndex = state.selectedChat?.flage === 'instagram' ? 1 : 0;
        const participantId = state.selectedChat?.participants?.data[participantIndex]?.id;

        if (participantId && state.latestMessages) {
          // Update the latestMessages array with the new message
          const updatedLatestMessages = state.latestMessages.map(msg => {
            if (msg.sender?.toString() === participantId?.toString() ||
              msg.recipient?.toString() === participantId?.toString()) {
              return {
                ...msg,
                message: latestMessage.message,
                created_time: latestMessage.created_time,
                updated_time: latestMessage.created_time,
              };
            }
            return msg;
          });

          // If no existing message found, add a new one
          const existingMessage = updatedLatestMessages.find(msg =>
            msg.sender?.toString() === participantId?.toString() ||
            msg.recipient?.toString() === participantId?.toString()
          );

          if (!existingMessage) {
            updatedLatestMessages.push({
              id: latestMessage.id,
              sender: participantId,
              recipient: state.selectedChat?.participants?.data[participantIndex === 1 ? 0 : 1]?.id,
              message: latestMessage.message,
              created_time: latestMessage.created_time,
              updated_time: latestMessage.created_time,
            });
          }

          state.latestMessages = updatedLatestMessages;
        }
      }
    },
    updateWhatsappChatMessages: (state, action) => {
      const newMessages = action.payload;

      // Ensure newMessages is an array
      if (!Array.isArray(newMessages)) {
        return;
      }

      const uniqueMessages = [...state.whatsappChatMessages];

      newMessages.forEach(newMsg => {
        const exists = uniqueMessages.some(msg =>
          msg.id === newMsg.id ||
          (msg.message_id && msg.message_id === newMsg.message_id) ||
          (msg.message === newMsg.message &&
            msg.sender === newMsg.sender &&
            msg.created_time === newMsg.created_time)
        );

        if (!exists) {
          console.log("Adding new message to chat:", newMsg);
          uniqueMessages.push(newMsg);
        } else {
          console.log("Message already exists, skipping:", newMsg.id);
        }
      });

      // Sort messages by creation time (ascending order for display)
      const sortedMessages = uniqueMessages.sort((a, b) => {
        const dateA = parseDate(a.created_time);
        const dateB = parseDate(b.created_time);
        return dateA - dateB;
      });

      state.whatsappChatMessages = sortedMessages;

      // Also update whatsappChats if we have a selected WhatsApp chat and new messages
      if (state.selectedWhatsappChat && newMessages.length > 0) {
        // Find the latest message from the new messages
        const latestMessage = newMessages.sort((a, b) => {
          const dateA = parseDate(a.created_time);
          const dateB = parseDate(b.created_time);
          return dateB - dateA; // Sort in descending order to get the latest
        })[0];

        // Update the selected WhatsApp chat with the latest message
        const updatedWhatsappChats = state.whatsappChats.map(chat => {
          // Match by sender_phone_number to ensure we update the correct chat
          if (chat.sender_phone_number === state.selectedWhatsappChat.sender_phone_number) {
            return {
              ...chat,
              last_message: {
                ...chat.last_message,
                message: latestMessage.message,
                from: latestMessage.sender,
                type: latestMessage.type || "text",
                message_id: latestMessage.message_id,
                timestamp: Math.floor(new Date(latestMessage.created_time).getTime() / 1000).toString(),
                created_at: latestMessage.created_time,
                updated_at: latestMessage.created_time,
                url: latestMessage.url || null
              },
              updated_time: latestMessage.created_time,
              created_time: latestMessage.created_time,
            };
          }
          return chat;
        });

        // Sort by latest message time
        const sortedChats = updatedWhatsappChats.sort((a, b) => {
          const timeA = a.last_message?.created_at
            ? parseDate(a.last_message.created_at)
            : a.updated_at
              ? parseDate(a.updated_at)
              : parseDate(a.created_at);

          const timeB = b.last_message?.created_at
            ? parseDate(b.last_message.created_at)
            : b.updated_at
              ? parseDate(b.updated_at)
              : parseDate(b.created_at);

          return timeB - timeA; // Sort in descending order (newest first)
        });

        state.whatsappChats = sortedChats;
      }

      // Also update latestMessages for the merged selector
      if (newMessages.length > 0 && state.selectedWhatsappChat) {
        // Find the latest message from the new messages
        const latestMessage = newMessages.sort((a, b) => {
          const dateA = parseDate(a.created_time);
          const dateB = parseDate(b.created_time);
          return dateB - dateA; // Sort in descending order to get the latest
        })[0];

        // Update latestMessages to include the latest WhatsApp message
        if (state.latestMessages) {
          const updatedLatestMessages = state.latestMessages.map(msg => {
            // Match by phone numbers - check if this message is for the current chat
            const msgSender = msg.sender?.toString();
            const msgRecipient = msg.recipient?.toString();
            const currentSender = state.selectedWhatsappChat.sender_phone_number?.toString();
            const currentRecipient = state.selectedPhone?.display_phone_number?.trim().replace(/^\+|\s+/g, '');

            if ((msgSender === currentSender && msgRecipient === currentRecipient) ||
              (msgSender === currentRecipient && msgRecipient === currentSender)) {
              return {
                ...msg,
                message: latestMessage.message,
                message_id: latestMessage.message_id,
                created_time: latestMessage.created_time,
                updated_time: latestMessage.created_time,
                created_at: latestMessage.created_time,
                url: latestMessage.url || null,
                type: latestMessage.type || "text"
              };
            }
            return msg;
          });

          // If no existing message was updated, add a new one
          const messageExists = updatedLatestMessages.some(msg => {
            const msgSender = msg.sender?.toString();
            const msgRecipient = msg.recipient?.toString();
            const currentSender = state.selectedWhatsappChat.sender_phone_number?.toString();
            const currentRecipient = state.selectedPhone?.display_phone_number?.trim().replace(/^\+|\s+/g, '');

            return (msgSender === currentSender && msgRecipient === currentRecipient) ||
              (msgSender === currentRecipient && msgRecipient === currentSender);
          });

          if (!messageExists) {
            updatedLatestMessages.push({
              id: state.selectedWhatsappChat.sender_phone_number,
              sender: latestMessage.sender,
              recipient: latestMessage.recipient,
              message: latestMessage.message,
              message_id: latestMessage.message_id,
              created_time: latestMessage.created_time,
              updated_time: latestMessage.created_time,
              created_at: latestMessage.created_time,
              type: latestMessage.type || "text",
              url: latestMessage.url || null
            });
          }

          state.latestMessages = updatedLatestMessages.sort((a, b) =>
            parseDate(b.created_time) - parseDate(a.created_time)
          );
        }
      }
    },
    clearErrors: (state) => {
      state.error = {
        fetchLatestMessages: null,
        fetchMessages: null,
        fetchWhatsAppMessages: null,
        sendMessage: null,
        sendWhatsMessage: null,
        fetchMoreChats: null,
        fetchChats: null,
      };
    },
    updateChats: (state, action) => {
      state.chats = action.payload(state.chats);
    },
    updateLatestMessages: (state, action) => {
      state.latestMessages = action.payload(state.latestMessages);
    },
    updateInstagramChats: (state, action) => {
      state.instagramChats = action.payload(state.instagramChats);
    },
    updateMessengerChats: (state, action) => {
      state.messengerChats = action.payload(state.messengerChats);
    },
    updateSelectedWhatsappChat: (state, action) => {
      state.selectedWhatsappChat = action.payload(state.selectedWhatsappChat);
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Latest Messages
      .addCase(fetchLatestMessages.pending, (state) => {
        state.loading.fetchingLatestMessages = true;
        state.error.fetchLatestMessages = null;
      })
      .addCase(fetchLatestMessages.fulfilled, (state, action) => {
        state.loading.fetchingLatestMessages = false;
        state.messagesSnapshotUnsubscribe = action.payload.unsubscribe;
      })
      .addCase(fetchLatestMessages.rejected, (state, action) => {
        state.loading.fetchingLatestMessages = false;
        state.error.fetchLatestMessages = action.payload;
      })

      // Fetch Messages
      .addCase(fetchMessages.pending, (state) => {
        state.loading.fetchingMessages = true;
        state.error.fetchMessages = null;
        state.nestedMessages = [];
        state.messages = [];
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        state.loading.fetchingMessages = false;
        state.nestedMessages = action.payload.nestedMessages;
        state.messages = action.payload.messages;
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        state.loading.fetchingMessages = false;
        state.error.fetchMessages = action.payload;
      })

      // Fetch WhatsApp Messages
      .addCase(fetchWhatsAppMessages.pending, (state) => {
        state.loading.fetchingWhatsAppMessages = true;
        state.error.fetchWhatsAppMessages = null;
      })
      .addCase(fetchWhatsAppMessages.fulfilled, (state, action) => {
        state.loading.fetchingWhatsAppMessages = false;

        // The messages are already set in the thunk, just update the chat and unsubscribe
        const { updatedChat, unsubscribe } = action.payload;

        state.selectedWhatsappChat = updatedChat;
        state.messagesSnapshotUnsubscribe = unsubscribe;
      })
      .addCase(fetchWhatsAppMessages.rejected, (state, action) => {
        state.loading.fetchingWhatsAppMessages = false;
        state.error.fetchWhatsAppMessages = action.payload;
      })

      // Fetch WhatsApp Latest Messages
      .addCase(fetchWhatsAppLatestMessages.fulfilled, (state, action) => {
        if (action.payload) {
          const { firebaseChats, unsubscribe } = action.payload;
          state.whatsappChats = firebaseChats;
          state.messagesSnapshotUnsubscribe = unsubscribe;
        }
      })

      // Fetch WhatsApp Chats From API
      .addCase(fetchWhatsAppChatsFromAPI.fulfilled, (state, action) => {
        // API chats synced to Firebase successfully
      })

      // Select Chat Async
      .addCase(selectChatAsync.fulfilled, (state, action) => {
        state.selectedChat = action.payload;
      })

      // Select WhatsApp Chat Async
      .addCase(selectWhatsappChatAsync.fulfilled, (state, action) => {
        state.selectedWhatsappChat = action.payload;
      })

      // Send Message
      .addCase(sendMessage.pending, (state) => {
        state.loading.sendingMessage = true;
        state.error.sendMessage = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.loading.sendingMessage = false;
        // The payload is now a boolean (true/false) indicating success/failure
        if (!action.payload) {
          state.error.sendMessage = "Failed to send message";
        }
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.loading.sendingMessage = false;
        state.error.sendMessage = action.payload || "Failed to send message";
      })

      // Send WhatsApp Message
      .addCase(sendWhatsMessage.pending, (state) => {
        state.loading.sendingWhatsMessage = true;
        state.error.sendWhatsMessage = null;
      })
      .addCase(sendWhatsMessage.fulfilled, (state, action) => {
        state.loading.sendingWhatsMessage = false;
      })
      .addCase(sendWhatsMessage.rejected, (state, action) => {
        state.loading.sendingWhatsMessage = false;
        state.error.sendWhatsMessage = action.payload || "Failed to send WhatsApp message";
      })

      // Listen to All WhatsApp Messages
      .addCase(listenToAllWhatsappMessages.fulfilled, (state, action) => {
        // Handle the logic for listening to all WhatsApp messages
      });
  }
});

// Export actions
export const {
  setChats,
  setSelectedChat,
  setMessages,
  setNestedMessages,
  setLatestMessages,
  setSelectedPage,
  setActiveFilter,
  setMessengerChats,
  setInstagramChats,
  setWhatsappChats,
  setWhatsappChatMessages,
  setWhatsAppAccounts,
  setSelectedWhatsAccount,
  setSelectedWhatsappChat,
  setWhatsAppAccountDetails,
  setSelectedPhone,
  setBusinessPhoneNumbers,
  setStartDate,
  setEndDate,
  setFilteredData,
  setConversations,
  setPaginationMeta,
  setHasMore,
  setDisabledChat,
  setDisabledChatLimit,
  setPageImg,
  setLoadingPagination,
  setLoadingChats,
  setMessagesSnapshotUnsubscribe,
  setLatestMessagesUnsubscribe,
  setPollingInterval,
  selectChat,
  selectWhatsappChat,
  handleSelectWhatsAppAccount,
  resetFilteredChatsPages,
  updateMessages,
  updateWhatsappChatMessages,
  updateWhatsappChatsWithFirebase,
  updateMessengerChats,
  updateInstagramChats,
  clearErrors,
  updateChats,
  updateLatestMessages,
  updateSelectedWhatsappChat,
} = metaBusinessSuiteSlice.actions;

// Export selectors
export const selectmetaBusinessSuite = (state) => state.metaBusinessSuite;
export const selectChats = (state) => state.metaBusinessSuite.chats;
export const selectWhatsappChats = (state) => state.metaBusinessSuite.whatsappChats;
export const selectSelectedChat = (state) => state.metaBusinessSuite.selectedChat;
export const selectSelectedWhatsappChat = (state) => state.metaBusinessSuite.selectedWhatsappChat;
export const selectMessages = createSelector(
  [state => state.metaBusinessSuite.nestedMessages, state => state.metaBusinessSuite.messages],
  (nestedMessages, messages) => [
    ...(nestedMessages || []),
    ...(messages || [])
  ]
);
export const selectLatestMessages = (state) => state.metaBusinessSuite.latestMessages;
export const selectWhatsappChatMessages = (state) => state.metaBusinessSuite.whatsappChatMessages;
export const selectSelectedPage = (state) => state.metaBusinessSuite.selectedPage;
export const selectActiveFilter = (state) => state.metaBusinessSuite.activeFilter;
export const selectLoadingStates = (state) => state.metaBusinessSuite.loading;
export const selectErrorStates = (state) => state.metaBusinessSuite.error;
export const selectDisabledChat = (state) => state.metaBusinessSuite.disabledChat;
export const selectDisabledChatLimit = (state) => state.metaBusinessSuite.disabledChatLimit;
export const selectPaginationMeta = (state) => state.metaBusinessSuite.paginationMeta;
export const selectLoadingPagination = (state) => state.metaBusinessSuite.loadingPagination;
export const selectInstagramChats = (state) => state.metaBusinessSuite.instagramChats;
export const selectMessengerChats = (state) => state.metaBusinessSuite.messengerChats;
export const selectLoadingChats = (state) => state.metaBusinessSuite.loadingChats;
export const selectHasMore = (state) => state.metaBusinessSuite.hasMore;
export const selectSelectedWhatsAccount = (state) => state.metaBusinessSuite.selectedWhatsAccount;
export const selectSelectedPhone = (state) => state.metaBusinessSuite.selectedPhone;
export const selectWhatsAppAccounts = (state) => state.metaBusinessSuite.whatsAppAccounts;
export const selectWhatsappLoading = (state) => state.metaBusinessSuite.loading;

// Memoized selector that merges latest messages with chats (same as Context API)
export const selectMergedAndSortedChats = createSelector(
  [
    state => state.metaBusinessSuite.activeFilter,
    state => state.metaBusinessSuite.messengerChats,
    state => state.metaBusinessSuite.instagramChats,
    selectLatestMessages
  ],
  (activeFilter, messengerChats, instagramChats, latestMessages) => {
    if (!activeFilter) {
      return [];
    }

    let baseChats = [];
    if (activeFilter === 'messenger') {
      baseChats = messengerChats || [];
    } else if (activeFilter === 'instagram') {
      baseChats = instagramChats || [];
    } else {
      return [];
    }

    if (!Array.isArray(baseChats) || baseChats.length === 0) {
      return baseChats;
    }

    // Use the same logic as the Context API
    const merged = baseChats.map(chat => {
      if (!chat || !chat.participants || !chat.participants.data) {
        return chat;
      }

      // Get the participant ID (same logic as Context)
      const participantIndex = activeFilter === 'instagram' ? 1 : 0;
      const participantId = chat.participants.data[participantIndex]?.id;

      if (!participantId) {
        return chat;
      }

      // Use the same extractLatestMessage function as Context
      const latestMsg = extractLatestMessage(participantId, latestMessages);

      if (latestMsg) {
        return {
          ...chat,
          message: latestMsg.message,
          updated_time: latestMsg.created_time,
          created_time: latestMsg.created_time,
          type: latestMsg.type || chat.type,
          url: latestMsg.url || chat.url,
        };
      }
      return chat;
    });

    // Sort by updated_time or created_time
    const sorted = merged.sort((a, b) => new Date(b.updated_time || b.created_time) - new Date(a.updated_time || a.created_time));
    return sorted;
  }
);

// Memoized selector for WhatsApp chats that merges with latest messages
export const selectMergedAndSortedWhatsappChats = createSelector(
  [
    state => state.metaBusinessSuite.whatsappChats,
    selectLatestMessages
  ],
  (whatsappChats, latestMessages) => {
    if (!Array.isArray(whatsappChats) || whatsappChats.length === 0) {
      return whatsappChats;
    }

    // Merge WhatsApp chats with latest messages
    const merged = whatsappChats.map(chat => {
      if (!chat) {
        return chat;
      }

      // For WhatsApp, we need to find the latest message for this chat
      // The chat has sender_phone_number, and messages have 'sender' or 'from' field
      const chatPhoneNumber = chat.sender_phone_number;

      if (!chatPhoneNumber) {
        return chat;
      }

      // Find all messages for this WhatsApp chat
      const chatMessages = latestMessages?.filter(msg => {
        // Check if the message is from or to this chat
        // Handle both 'sender' and 'from' fields for compatibility
        const msgSender = msg.sender || msg.from;
        const msgRecipient = msg.recipient;

        const isMatch = msgSender === chatPhoneNumber || msgRecipient === chatPhoneNumber;
        return isMatch;
      });

      if (chatMessages && chatMessages.length > 0) {
        // Find the latest message
        const latestMsg = chatMessages.sort((a, b) => {
          const dateA = new Date(a.created_time || a.created_at);
          const dateB = new Date(b.created_time || b.created_at);
          return dateB - dateA; // Sort in descending order
        })[0];

        return {
          ...chat,
          last_message: {
            message: latestMsg.message,
            created_at: latestMsg.created_time || latestMsg.created_at,
            updated_at: latestMsg.created_time || latestMsg.created_at,
            from: latestMsg.sender || latestMsg.from,
            type: latestMsg.type || "text",
            message_id: latestMsg.message_id,
            timestamp: Math.floor(new Date(latestMsg.created_time || latestMsg.created_at).getTime() / 1000).toString(),
            url: latestMsg.url || null
          },
          updated_at: latestMsg.created_time || latestMsg.created_at,
        };
      }
      return chat;
    });

    // Sort by updated_at or created_at
    const sorted = merged.sort((a, b) => {
      const dateA = a.last_message?.created_at || a.updated_at || a.created_at;
      const dateB = b.last_message?.created_at || b.updated_at || b.created_at;
      return new Date(dateB) - new Date(dateA);
    });

    return sorted;
  }
);

// Helper function to format date (moved from context)
export const formatDate = (timestamp) => {
  let messageDate;
  if (timestamp?.seconds && timestamp?.nanoseconds) {
    const milliseconds =
      timestamp.seconds * 1000 + timestamp.nanoseconds / 1e6;
    messageDate = new Date(milliseconds);
  } else {
    try {
      messageDate = new Date(timestamp);
    } catch (error) {
      console.error("Error parsing timestamp:", error);
    }
  }

  const currentDate = new Date();
  return formatTimestamp(messageDate, currentDate);
};

export default metaBusinessSuiteSlice.reducer;
