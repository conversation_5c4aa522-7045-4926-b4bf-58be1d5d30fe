import { Col, Form, InputGroup, Pagination, Row, Table } from "react-bootstrap";
import { useEffect, useMemo, useState } from "react";
import {
  useAsyncDebounce,
  useFilters,
  useGlobalFilter,
  usePagination,
  useSortBy,
  useTable,
} from "react-table";
import { BiSearch } from "react-icons/bi";
import { BsFillCaretDownFill } from "react-icons/bs";
import CenteredModal from "../Shared/modals/CenteredModal/CenteredModal";
import InviteMemberModal from "./InviteMemberModal";
import FetchingDataLoading from "../LoadingAnimation/FetchingDataLoading";
import { useSelector, useDispatch } from "react-redux";
import { setRoles } from "../../redux/features/roleSlice";
import getAllRolesApi from "../../services/roles/get-all-roles.api";
import ConfirmModal from "../Modals/ConfirmModal";
import leadService from "../../services/leads";
import { Toolt<PERSON> } from "react-tooltip";
import "./team-members-table.css";
import { FaFilterCircleXmark } from "react-icons/fa6";
import { FcInvite } from "react-icons/fc";
import { TbAutomaticGearbox } from "react-icons/tb";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { shouldShowPackageFeatures } from "../../config/packageVisibility";

function GlobalFilter({
  preGlobalFilteredRows,
  globalFilter,
  setGlobalFilter,
}) {
  const { t } = useTranslation();
  const count = preGlobalFilteredRows.length;
  const [value, setValue] = useState(globalFilter);
  const onChange = useAsyncDebounce((value) => {
    setGlobalFilter(value || undefined);
  }, 200);

  // Add this to expose setValue to parent
  useEffect(() => {
    if (globalFilter === "") {
      setValue("");
    }
  }, [globalFilter]);

  return (
    <div className={"position-relative"}>
      <InputGroup>
        <Form.Control
          aria-label="Default"
          aria-describedby="inputGroup-sizing-default"
          value={value || ""}
          onChange={(e) => {
            setValue(e.target.value);
            onChange(e.target.value);
          }}
          placeholder={t("teamMembers.searchRecordsCount", { count })}
          className={"search-input"}
        />
      </InputGroup>
      <div className={"search-icon"}>
        <BiSearch color={"#000"} size={20} />
      </div>
    </div>
  );
}

function DefaultColumnFilter({
  column: { filterValue, preFilteredRows, setFilter },
}) {
  const count = preFilteredRows.length;

  return (
    <input
      className="form-control"
      value={filterValue || ""}
      onChange={(e) => {
        setFilter(e.target.value || undefined);
      }}
      placeholder={`Search ${count} records...`}
    />
  );
}

const TeamMembersTable = ({ data, columns, loading }) => {
  const { t } = useTranslation();
  const defaultColumn = useMemo(
    () => ({
      // Default Filter UI
      Filter: DefaultColumnFilter,
    }),
    []
  );
  const [showCenteredModal, setShowCenteredModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    preGlobalFilteredRows,
    setGlobalFilter,
    setFilter,
    page,
    canPreviousPage,
    canNextPage,
    pageOptions,
    gotoPage,
    nextPage,
    previousPage,
    setPageSize,
    state: { pageIndex, pageSize, globalFilter },
  } = useTable(
    {
      columns,
      data,
      defaultColumn,
      initialState: { pageIndex: 0, pageSize: 5 },
    },
    useFilters,
    useGlobalFilter,
    useSortBy,
    usePagination
  );

  const handleClearFilters = () => {
    setGlobalFilter(""); // This will trigger the useEffect in GlobalFilter
    columns.forEach((column) => {
      if (column.accessor) {
        setFilter(column.accessor, undefined);
      }
    });
  };
  const { user, currentUserPermissions } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  useEffect(() => {
    const fetchRoles = async () => {
      const roleType = user.user.flag === "admin" ? "admin" : "user";
      const response = currentUserPermissions.includes("role-list")
        ? await getAllRolesApi(roleType)
        : null;
      dispatch(setRoles(response?.data));
    };
    fetchRoles();
  }, [dispatch, user, currentUserPermissions]);

  const { disableAddMember } = useSelector((state) => state.client);
  const getQuotaMessage = () => {
    if (user?.user?.package_id === 1) {
      // Free plan
      return t("team.quotaExceeded", { limits: "3", plan: "Plus" });
    } else if (user?.user?.package_id === 2) {
      // Plus plan
      return t("team.quotaExceeded", { limits: "20", plan: "Custom" });
    }
    return "";
  };

  return (
    <>
      <h2 className={"page-title"}>{t("pageHeaders.teamMembers")}</h2>
      {loading ? (
        <FetchingDataLoading className={"content-container"} />
      ) : (
        <div className={"all-leads-table content-container"}>
          {disableAddMember && shouldShowPackageFeatures(user?.user?.id) && (
            <>
              <div className="quota-container">
                <div className="radial-gradient-border">
                  {getQuotaMessage()}
                </div>
                <Link to={"/packages"}>
                  <button className="submit-btn">
                    {t("common.upgradeNow")}
                  </button>
                </Link>
              </div>
            </>
          )}
          <Row className={"m-4"}>
            <Row className={"justify-content-between align-items-center"}>
              {currentUserPermissions?.includes("member-list") ? (
                <Col lg={5} className={"mt-3"}>
                  <GlobalFilter
                    preGlobalFilteredRows={preGlobalFilteredRows}
                    globalFilter={globalFilter}
                    setGlobalFilter={setGlobalFilter}
                  />
                </Col>
              ) : null}

              <Col
                className={
                  "d-flex justify-content-center justify-content-lg-end mt-3"
                }
                lg={7}
              >
                <Tooltip
                  anchorSelect="#auto-assign"
                  className={"bg-dark text-white"}
                >
                  <div className={"d-flex flex-column align-items-center"}>
                    <span>{t("team.assignUnassigned")}</span>
                    <span>{t("team.basedOnWorkload")}</span>
                  </div>
                </Tooltip>
                {currentUserPermissions?.includes("member-list") ? (
                  <>
                    <div className={"shadow-sm rounded-2 mx-4 p-1"}>
                      <FaFilterCircleXmark
                        role={"button"}
                        size={25}
                        onClick={handleClearFilters}
                        id={"clear-filter"}
                      />
                    </div>

                    <Tooltip
                      anchorSelect="#clear-filter"
                      className={"bg-dark text-white"}
                      content={t("common.clearSearch")}
                    />
                  </>
                ) : null}
                {currentUserPermissions?.includes("member-create") &&
                !disableAddMember ? (
                  <>
                    <div className={"shadow-sm rounded-2 p-1"}>
                      <FcInvite
                        role={"button"}
                        id={"invite-new-member"}
                        size={25}
                        onClick={() => setShowCenteredModal(true)}
                      />
                    </div>
                    <Tooltip
                      anchorSelect="#invite-new-member"
                      className={"bg-dark text-white"}
                      content={t("inviteMember.title")}
                    />
                  </>
                ) : null}
              </Col>
            </Row>
          </Row>
          {currentUserPermissions?.includes("member-list") ? (
            <Table
              responsive={"lg"}
              className="text-center"
              {...getTableProps()}
            >
              <thead>
                {headerGroups.map((headerGroup, index) => (
                  <tr {...headerGroup.getHeaderGroupProps()} key={index}>
                    {headerGroup.headers.map((column, i) => (
                      <th
                        {...column.getHeaderProps(
                          column.getSortByToggleProps()
                        )}
                        key={i}
                      >
                        {column.render("Header")}
                        {/* Render the columns filter UI */}
                        {/*<div>{column.canFilter ? column.render('Filter') : null}</div>*/}
                        <span>
                          {column.isSorted ? (
                            column.isSortedDesc ? (
                              " 🔽"
                            ) : (
                              " 🔼"
                            )
                          ) : (
                            <> {column.accessor && <BsFillCaretDownFill />}</>
                          )}
                        </span>
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody {...getTableBodyProps()}>
                {page.map((row) => {
                  prepareRow(row);
                  return (
                    <tr
                      {...row.getRowProps()}
                      className={"client-table-row filter-table-rows"}
                      key={row.original.id}
                    >
                      {row.cells.map((cell, i) => {
                        return (
                          <td {...cell.getCellProps()} key={i}>
                            {cell.render("Cell")}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </Table>
          ) : (
            <div className={"mainColor fw-bold fs-2 text-center"}>
              {t("common.notAuthorized")}
            </div>
          )}
          {currentUserPermissions?.includes("member-list") ? (
            <div
              className={
                "d-flex justify-content-between flex-column flex-md-row align-items-center my-4 mx-3"
              }
            >
              <div
                className={
                  "d-flex flex-column justify-content-center align-items-center"
                }
              >
                <div className={"mb-1"}>{t("filters.recordsPerPage")}</div>
                <div
                  className="btn-group records-buttons-container"
                  role="group"
                >
                  {[5, 10, 20, 30, 40, 50].map((pageSizeOption) => (
                    <div
                      key={pageSizeOption}
                      role="button"
                      className={`${
                        pageSize === pageSizeOption
                          ? "record-button-selected"
                          : "record-button"
                      }`}
                      onClick={() => setPageSize(pageSizeOption)}
                    >
                      {pageSizeOption}
                    </div>
                  ))}
                </div>
              </div>
              <Pagination className={"data-table-pagination"}>
                <Pagination.Prev
                  onClick={() => previousPage()}
                  disabled={!canPreviousPage}
                />
                {Array.from({ length: pageOptions.length }).map((_, index) => {
                  if (
                    pageOptions.length <= 5 ||
                    index === 0 ||
                    index === pageOptions.length - 1 ||
                    (index >= pageIndex - 2 && index <= pageIndex + 2)
                  ) {
                    return (
                      <Pagination.Item
                        key={index}
                        onClick={() => gotoPage(index)}
                        active={pageIndex === index}
                      >
                        {index + 1}
                      </Pagination.Item>
                    );
                  } else if (index === 1 || index === pageOptions.length - 2) {
                    return <Pagination.Ellipsis key={index} />;
                  }
                  return null;
                })}
                <Pagination.Next
                  onClick={() => nextPage()}
                  disabled={!canNextPage}
                />
              </Pagination>
            </div>
          ) : null}
        </div>
      )}

      <CenteredModal
        size={"lg"}
        show={showCenteredModal}
        children={
          <InviteMemberModal handleClose={() => setShowCenteredModal(false)} />
        }
        onHide={() => setShowCenteredModal(false)}
      />
    </>
  );
};

export default TeamMembersTable;
