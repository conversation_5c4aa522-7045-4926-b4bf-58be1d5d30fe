# Design Document

## Overview

This feature implements a user-specific visibility control system that conditionally hides all package/subscription-related functionality for specific users. The system will use a configurable approach to identify excluded users and apply visibility controls across the entire application.

## Architecture

The solution follows a centralized configuration approach with distributed implementation across UI components. The architecture consists of:

1. **Configuration Layer**: A centralized configuration system to manage excluded user IDs
2. **Utility Layer**: A helper function to check if package features should be hidden for a user
3. **Component Layer**: Conditional rendering logic applied to relevant UI components
4. **Route Layer**: Route-level protection for package-related pages

## Components and Interfaces

### 1. Configuration System

**Location**: `src/config/packageVisibility.js`

```javascript
// Configuration for users who should not see package-related features
export const PACKAGE_EXCLUDED_USERS = [423];

// Helper function to check if user should see package features
export const shouldShowPackageFeatures = (userId) => {
  return !PACKAGE_EXCLUDED_USERS.includes(Number(userId));
};
```

### 2. Sidebar Navigation Updates

**Component**: `src/components/Layout/Sidebar/Sidebar.component.jsx`

The sidebar contains two package-related menu items:
- ID 9: "Packages" (link: "packages")
- ID 10: "Subscription" (link: "invoices-and-subscriptions")

These items will be filtered out for excluded users in the `mainMenuItems` array.

### 3. Route Protection

**Component**: `src/App.js`

Package-related routes that need protection:
- `/packages` - Main packages page
- `/invoices-and-subscriptions` - Subscription details page
- `/payment/success` - Payment success page
- `/payment/failed` - Payment failure page
- `/subscription` - Subscription page
- `/package-purchase` - Package purchase page

### 4. Package Warnings and Modals

Based on the codebase analysis, package-related warnings and modals are likely present in:
- Redux state management (`authSlice.js` - `packageData`, `handlePurchase`)
- Various components that check package status
- Payment and subscription related components

### 5. Auth Slice Updates

**Component**: `src/redux/features/authSlice.js`

The auth slice contains package-related state and actions that need conditional handling:
- `packageData` state
- `setPackageData` action
- `handlePurchase` async thunk

## Data Models

### User Exclusion Configuration

```javascript
{
  excludedUserIds: [423], // Array of user IDs to exclude
  isEnabled: true // Feature flag to enable/disable the functionality
}
```

### Package Visibility State

```javascript
{
  userId: number,
  shouldShowPackages: boolean,
  shouldShowSubscriptions: boolean,
  shouldShowUpgradePrompts: boolean
}
```

## Error Handling

1. **Invalid User ID**: If user ID is null/undefined, default to showing package features
2. **Configuration Errors**: If configuration file is missing, default to showing all features
3. **Route Access**: Redirect excluded users to unauthorized page if they try to access package routes directly
4. **API Errors**: Handle cases where package-related API calls fail gracefully for excluded users

## Testing Strategy

### Unit Tests

1. **Configuration Tests**
   - Test `shouldShowPackageFeatures()` with various user IDs
   - Test with excluded user ID (423)
   - Test with non-excluded user IDs
   - Test with invalid/null user IDs

2. **Component Tests**
   - Test sidebar rendering with and without package links
   - Test route protection for excluded users
   - Test conditional rendering of package-related UI elements

3. **Integration Tests**
   - Test complete user flow for excluded user (no package features visible)
   - Test complete user flow for regular user (all features visible)
   - Test navigation restrictions

### Test Scenarios

1. **Excluded User (ID 423)**
   - Should not see "Packages" or "Subscription" in sidebar
   - Should be redirected when accessing package routes directly
   - Should not see package warnings or upgrade modals
   - Should not have access to package-related API calls

2. **Regular User (Any other ID)**
   - Should see all package-related features normally
   - Should have full access to all package functionality
   - Should see warnings and modals as expected

3. **Edge Cases**
   - User with null/undefined ID
   - User switching between excluded and non-excluded accounts
   - Configuration changes during active session

## Implementation Approach

### Phase 1: Core Infrastructure
- Create configuration system
- Implement utility functions
- Add unit tests for core functionality

### Phase 2: UI Component Updates
- Update sidebar navigation
- Add conditional rendering to package-related components
- Implement route protection

### Phase 3: State Management
- Update Redux slices to handle excluded users
- Modify package-related actions and reducers
- Add state-based conditional logic

### Phase 4: Testing and Validation
- Comprehensive testing across all affected components
- User acceptance testing with excluded user account
- Performance testing to ensure no impact on regular users

## Security Considerations

1. **Client-Side Only**: This is a UI-level feature and should not be relied upon for security
2. **Server-Side Validation**: Backend should also validate user permissions for package operations
3. **Route Protection**: Implement proper route guards to prevent direct URL access
4. **State Management**: Ensure excluded users cannot manipulate client state to bypass restrictions

## Performance Considerations

1. **Minimal Impact**: Configuration check should be lightweight and cached
2. **Lazy Loading**: Package-related components can be conditionally loaded
3. **Memory Usage**: Avoid loading package-related data for excluded users
4. **Bundle Size**: Consider code splitting for package-related functionality
